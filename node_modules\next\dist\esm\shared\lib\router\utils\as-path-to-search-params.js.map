{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/as-path-to-search-params.ts"], "sourcesContent": ["// Convert router.asPath to a URLSearchParams object\n// example: /dynamic/[slug]?foo=bar -> { foo: 'bar' }\nexport function asPathToSearchParams(asPath: string): URLSearchParams {\n  return new URL(asPath, 'http://n').searchParams\n}\n"], "names": ["asPathToSearchParams", "<PERSON><PERSON><PERSON>", "URL", "searchParams"], "mappings": "AAAA,oDAAoD;AACpD,qDAAqD;AACrD,OAAO,SAASA,qBAAqBC,MAAc;IACjD,OAAO,IAAIC,IAAID,QAAQ,YAAYE,YAAY;AACjD"}
'use client';

import { useState, useEffect, FormEvent, useCallback, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { type NewApiKey, type DisplayApiKey } from '@/types/apiKeys';
import { llmProviders } from '@/config/models';
import { PREDEFINED_ROLES, Role, getRoleById } from '@/config/roles';
import { ArrowLeftIcon, TrashIcon, Cog6ToothIcon, CheckCircleIcon, ShieldCheckIcon, InformationCircleIcon, CloudArrowDownIcon, PlusCircleIcon, XCircleIcon, PlusIcon, KeyIcon, PencilIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'react-tooltip';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import { useManageKeysPrefetch } from '@/hooks/useManageKeysPrefetch';
import ManageKeysLoadingSkeleton, { CompactManageKeysLoadingSkeleton } from '@/components/ManageKeysLoadingSkeleton';
import { useRoutingSetupPrefetch } from '@/hooks/useRoutingSetupPrefetch';
import { useNavigationSafe } from '@/contexts/NavigationContext';
import { ApiKeyManager } from '@/components/UserApiKeys/ApiKeyManager';

// Interface for the richer ModelInfo from /api/providers/list-models
interface FetchedModelInfo {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  version?: string;
  family?: string;
  input_token_limit?: number;
  output_token_limit?: number;
  context_window?: number;
  modality?: string;
  provider_id?: string;
  provider_specific?: Record<string, any>;
}

// Type for a single custom API configuration (matching MyModelsPage)
interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
}

interface ApiKeyWithRoles extends DisplayApiKey {
  assigned_roles: Role[];
  // Note: is_default_general_chat_model is already included from DisplayApiKey
}

// Updated: Interface for User-Defined Custom Roles (now global)
interface UserCustomRole {
  id: string; // UUID for the custom role entry itself (database ID)
  user_id: string; // Belongs to this user
  role_id: string; // The user-defined short ID (e.g., "my_summarizer"), unique per user
  name: string;    // User-friendly name
  description?: string | null;
  created_at: string;
  updated_at: string;
}

export default function ManageKeysPage() {
  const params = useParams();
  const router = useRouter();
  const configId = params.configId as string;
  
  // Navigation safety
  const { isNavigating } = useNavigationSafe();

  // State for tabs
  const [activeTab, setActiveTab] = useState<'api-keys' | 'user-api-keys'>('api-keys');

  // State for configuration details
  const [configDetails, setConfigDetails] = useState<CustomApiConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState<boolean>(true);

  // State for adding new API keys
  const [provider, setProvider] = useState<string>('openai');
  const [predefinedModelId, setPredefinedModelId] = useState<string>('');
  const [apiKeyRaw, setApiKeyRaw] = useState<string>('');
  const [label, setLabel] = useState<string>('');
  const [temperature, setTemperature] = useState<number>(1.0);
  const [isSavingKey, setIsSavingKey] = useState<boolean>(false);

  // State for messages
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for user custom roles
  const [userCustomRoles, setUserCustomRoles] = useState<UserCustomRole[]>([]);
  const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = useState<boolean>(true);

  // State for dynamic model fetching
  const [fetchedProviderModels, setFetchedProviderModels] = useState<FetchedModelInfo[] | null>(null);
  const [isFetchingProviderModels, setIsFetchingProviderModels] = useState<boolean>(false);
  const [fetchProviderModelsError, setFetchProviderModelsError] = useState<string | null>(null);

  const [savedKeysWithRoles, setSavedKeysWithRoles] = useState<ApiKeyWithRoles[]>([]);
  const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = useState<boolean>(true);
  const [isDeletingKey, setIsDeletingKey] = useState<string | null>(null);
  const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = useState<string | null>(null);

  const [editingRolesApiKey, setEditingRolesApiKey] = useState<ApiKeyWithRoles | null>(null);

  // State for editing API keys
  const [editingApiKey, setEditingApiKey] = useState<ApiKeyWithRoles | null>(null);
  const [editTemperature, setEditTemperature] = useState<number>(1.0);
  const [editPredefinedModelId, setEditPredefinedModelId] = useState<string>('');
  const [isSavingEdit, setIsSavingEdit] = useState<boolean>(false);

  // Confirmation modal hook
  const confirmation = useConfirmation();

  // Prefetch hooks
  const { getCachedData, setCachedData } = useManageKeysPrefetch();
  const { prefetchRoutingSetupData } = useRoutingSetupPrefetch();

  // Memoized provider options
  const PROVIDER_OPTIONS = useMemo(() => 
    llmProviders.map(provider => ({
      value: provider.id,
      label: provider.name
    })), []);

  // Memoized model options based on selected provider
  const modelOptions = useMemo(() => {
    const selectedProvider = llmProviders.find(p => p.id === provider);
    if (!selectedProvider) return [];
    
    // Use fetched models if available, otherwise fall back to predefined models
    const modelsToUse = fetchedProviderModels || selectedProvider.models;
    
    return modelsToUse.map(model => ({
      value: model.id,
      label: model.display_name || model.name
    }));
  }, [provider, fetchedProviderModels]);

  // Fetch configuration details
  useEffect(() => {
    const fetchConfigDetails = async () => {
      if (!configId) return;
      
      setIsLoadingConfig(true);
      setError(null);
      
      try {
        const response = await fetch('/api/custom-configs');
        if (!response.ok) {
          throw new Error('Failed to fetch configurations');
        }
        
        const configs: CustomApiConfig[] = await response.json();
        const config = configs.find(c => c.id === configId);
        
        if (!config) {
          throw new Error('Configuration not found');
        }
        
        setConfigDetails(config);
      } catch (err: any) {
        setError(`Error loading model configuration: ${err.message}`);
      } finally {
        setIsLoadingConfig(false);
      }
    };

    fetchConfigDetails();
  }, [configId]);

  // Fetch user custom roles
  useEffect(() => {
    const fetchUserCustomRoles = async () => {
      setIsLoadingUserCustomRoles(true);
      try {
        const response = await fetch('/api/user/custom-roles');
        if (!response.ok) {
          throw new Error('Failed to fetch user custom roles');
        }
        const roles: UserCustomRole[] = await response.json();
        setUserCustomRoles(roles);
      } catch (err: any) {
        console.error('Error fetching user custom roles:', err);
        // Don't set error state for this, as it's not critical
      } finally {
        setIsLoadingUserCustomRoles(false);
      }
    };

    fetchUserCustomRoles();
  }, []);

  // Set initial model when provider changes
  useEffect(() => {
    if (modelOptions.length > 0 && !predefinedModelId) {
      setPredefinedModelId(modelOptions[0].value);
    }
  }, [modelOptions, predefinedModelId]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/my-models')}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
                disabled={isNavigating}
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                Back to My Models
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {isLoadingConfig ? 'Loading...' : configDetails?.name || 'Manage Keys'}
                </h1>
                <p className="text-sm text-gray-500">Configure API keys and settings</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('api-keys')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'api-keys'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <KeyIcon className="h-5 w-5 inline mr-2" />
              API Keys
            </button>
            <button
              onClick={() => setActiveTab('user-api-keys')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'user-api-keys'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <GlobeAltIcon className="h-5 w-5 inline mr-2" />
              User API Keys
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* User-Generated API Keys Tab */}
        {activeTab === 'user-api-keys' && configDetails && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <ApiKeyManager
              configId={configId}
              configName={configDetails.name}
            />
          </div>
        )}
        
        {/* API Keys Tab - Placeholder for now */}
        {activeTab === 'api-keys' && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div className="text-center py-8">
              <KeyIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">API Keys Management</h3>
              <p className="text-gray-600">
                This section will contain the existing API keys management functionality.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect, FormEvent } from 'react';
import Link from 'next/link';
import { PlusCircleIcon, TrashIcon, PencilIcon, KeyIcon, CalendarIcon } from '@heroicons/react/24/outline';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useManageKeysPrefetch } from '@/hooks/useManageKeysPrefetch';

// Type for a custom API configuration (can be expanded later)
interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
}

export default function MyModelsPage() {
  const [configs, setConfigs] = useState<CustomApiConfig[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [newConfigName, setNewConfigName] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);

  // Confirmation modal hook
  const confirmation = useConfirmation();
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);

  // Prefetch hook for manage keys pages
  const { createHoverPrefetch, prefetchManageKeysData } = useManageKeysPrefetch();

  // Fetch configurations on component mount
  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/custom-configs');
      if (!response.ok) {
        throw new Error('Failed to fetch configurations');
      }
      const data: CustomApiConfig[] = await response.json();
      setConfigs(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateConfig = async (e: FormEvent) => {
    e.preventDefault();
    if (!newConfigName.trim()) return;

    setIsCreating(true);
    setError(null);
    try {
      const response = await fetch('/api/custom-configs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newConfigName.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create configuration');
      }

      const newConfig: CustomApiConfig = await response.json();
      setConfigs(prev => [newConfig, ...prev]);
      setNewConfigName('');
      setShowCreateForm(false);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteConfig = (configId: string, configName: string) => {
    confirmation.confirm({
      title: 'Delete Configuration',
      message: `Are you sure you want to delete "${configName}"? This action cannot be undone and will also delete all associated API keys.`,
      confirmText: 'Delete',
      isDestructive: true,
      onConfirm: () => deleteConfig(configId),
    });
  };

  const deleteConfig = async (configId: string) => {
    try {
      const response = await fetch(`/api/custom-configs/${configId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete configuration');
      }

      setConfigs(prev => prev.filter(config => config.id !== configId));
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Models</h1>
              <p className="text-sm text-gray-500">Manage your API configurations and keys</p>
            </div>
            <Button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center gap-2"
            >
              <PlusCircleIcon className="h-5 w-5" />
              New Configuration
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Create Form */}
        {showCreateForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Create New Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateConfig} className="space-y-4">
                <div>
                  <label htmlFor="configName" className="block text-sm font-medium text-gray-700 mb-2">
                    Configuration Name
                  </label>
                  <input
                    id="configName"
                    type="text"
                    value={newConfigName}
                    onChange={(e) => setNewConfigName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter configuration name"
                    required
                  />
                </div>
                <div className="flex gap-3">
                  <Button type="submit" disabled={isCreating}>
                    {isCreating ? 'Creating...' : 'Create Configuration'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowCreateForm(false);
                      setNewConfigName('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Configurations Grid */}
        {configs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <KeyIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Configurations</h3>
              <p className="text-gray-600 mb-6">
                Create your first API configuration to get started with RouKey.
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <PlusCircleIcon className="h-5 w-5 mr-2" />
                Create Your First Configuration
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {configs.map((config) => (
              <Card key={config.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {config.name}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        Created: {new Date(config.created_at).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100">
                      <KeyIcon className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3 mt-6">
                    <Link
                      href={`/my-models/${config.id}`}
                      className="flex-1"
                      {...createHoverPrefetch(config.id)}
                    >
                      <Button className="w-full" size="sm">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Manage Keys
                      </Button>
                    </Link>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteConfig(config.id, config.name)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.close}
        onConfirm={confirmation.handleConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        isDestructive={confirmation.isDestructive}
      />
    </div>
  );
}

{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-pages-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON> } from './helpers/file-reader/file-reader'\nimport {\n  PagesRouteMatcher,\n  PagesLocaleRouteMatcher,\n} from '../../route-matchers/pages-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport path from 'path'\nimport type { LocaleRouteNormalizer } from '../../normalizers/locale-route-normalizer'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\nimport { DevPagesNormalizers } from '../../normalizers/built/pages'\n\nexport class DevPagesRouteMatcherProvider extends FileCacheRouteMatcherProvider<PagesRouteMatcher> {\n  private readonly expression: RegExp\n  private readonly normalizers: DevPagesNormalizers\n\n  constructor(\n    private readonly pagesDir: string,\n    private readonly extensions: ReadonlyArray<string>,\n    reader: FileReader,\n    private readonly localeNormalizer?: LocaleRouteNormalizer\n  ) {\n    super(pagesDir, reader)\n\n    // Match any route file that ends with `/${filename}.${extension}` under the\n    // pages directory.\n    this.expression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    this.normalizers = new DevPagesNormalizers(pagesDir, extensions)\n  }\n\n  private test(filename: string): boolean {\n    // If the file does not end in the correct extension it's not a match.\n    if (!this.expression.test(filename)) return false\n\n    // Pages routes must exist in the pages directory without the `/api/`\n    // prefix. The pathnames being tested here though are the full filenames,\n    // so we need to include the pages directory.\n\n    // TODO: could path separator normalization be needed here?\n    if (filename.startsWith(path.join(this.pagesDir, '/api/'))) return false\n\n    for (const extension of this.extensions) {\n      // We can also match if we have `pages/api.${extension}`, so check to\n      // see if it's a match.\n      if (filename === path.join(this.pagesDir, `api.${extension}`)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<PagesRouteMatcher>> {\n    const matchers: Array<PagesRouteMatcher> = []\n    for (const filename of files) {\n      // If the file isn't a match for this matcher, then skip it.\n      if (!this.test(filename)) continue\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const page = this.normalizers.page.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n\n      if (this.localeNormalizer) {\n        matchers.push(\n          new PagesLocaleRouteMatcher({\n            kind: RouteKind.PAGES,\n            pathname,\n            page,\n            bundlePath,\n            filename,\n            i18n: {},\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesRouteMatcher({\n            kind: RouteKind.PAGES,\n            pathname,\n            page,\n            bundlePath,\n            filename,\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["PagesRouteMatcher", "PagesLocaleRouteMatcher", "RouteKind", "path", "FileCacheRouteMatcherProvider", "DevPagesNormalizers", "DevPagesRouteMatcherProvider", "constructor", "pagesDir", "extensions", "reader", "localeNormalizer", "expression", "RegExp", "join", "normalizers", "test", "filename", "startsWith", "extension", "transform", "files", "matchers", "pathname", "normalize", "page", "bundlePath", "push", "kind", "PAGES", "i18n"], "mappings": "AACA,SACEA,iBAAiB,EACjBC,uBAAuB,QAClB,2CAA0C;AACjD,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,OAAOC,UAAU,OAAM;AAEvB,SAASC,6BAA6B,QAAQ,sCAAqC;AACnF,SAASC,mBAAmB,QAAQ,gCAA+B;AAEnE,OAAO,MAAMC,qCAAqCF;IAIhDG,YACE,AAAiBC,QAAgB,EACjC,AAAiBC,UAAiC,EAClDC,MAAkB,EAClB,AAAiBC,gBAAwC,CACzD;QACA,KAAK,CAACH,UAAUE,cALCF,WAAAA,eACAC,aAAAA,iBAEAE,mBAAAA;QAIjB,4EAA4E;QAC5E,mBAAmB;QACnB,IAAI,CAACC,UAAU,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAEJ,WAAWK,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9D,IAAI,CAACC,WAAW,GAAG,IAAIV,oBAAoBG,UAAUC;IACvD;IAEQO,KAAKC,QAAgB,EAAW;QACtC,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAACL,UAAU,CAACI,IAAI,CAACC,WAAW,OAAO;QAE5C,qEAAqE;QACrE,yEAAyE;QACzE,6CAA6C;QAE7C,2DAA2D;QAC3D,IAAIA,SAASC,UAAU,CAACf,KAAKW,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,WAAW,OAAO;QAEnE,KAAK,MAAMW,aAAa,IAAI,CAACV,UAAU,CAAE;YACvC,qEAAqE;YACrE,uBAAuB;YACvB,IAAIQ,aAAad,KAAKW,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,CAAC,IAAI,EAAEW,WAAW,GAAG;gBAC7D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAgBC,UACdC,KAA4B,EACe;QAC3C,MAAMC,WAAqC,EAAE;QAC7C,KAAK,MAAML,YAAYI,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACL,IAAI,CAACC,WAAW;YAE1B,MAAMM,WAAW,IAAI,CAACR,WAAW,CAACQ,QAAQ,CAACC,SAAS,CAACP;YACrD,MAAMQ,OAAO,IAAI,CAACV,WAAW,CAACU,IAAI,CAACD,SAAS,CAACP;YAC7C,MAAMS,aAAa,IAAI,CAACX,WAAW,CAACW,UAAU,CAACF,SAAS,CAACP;YAEzD,IAAI,IAAI,CAACN,gBAAgB,EAAE;gBACzBW,SAASK,IAAI,CACX,IAAI1B,wBAAwB;oBAC1B2B,MAAM1B,UAAU2B,KAAK;oBACrBN;oBACAE;oBACAC;oBACAT;oBACAa,MAAM,CAAC;gBACT;YAEJ,OAAO;gBACLR,SAASK,IAAI,CACX,IAAI3B,kBAAkB;oBACpB4B,MAAM1B,UAAU2B,KAAK;oBACrBN;oBACAE;oBACAC;oBACAT;gBACF;YAEJ;QACF;QAEA,OAAOK;IACT;AACF"}
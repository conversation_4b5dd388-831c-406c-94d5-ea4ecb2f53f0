'use client';

import { useCallback } from 'react';

interface CachedData {
  apiKeys?: any[];
  defaultChatKeyId?: string | null;
  userCustomRoles?: any[];
  configDetails?: any;
  timestamp?: number;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map<string, CachedData>();

export function useManageKeysPrefetch() {
  const getCachedData = useCallback((configId: string): CachedData | null => {
    const cached = cache.get(configId);
    if (!cached || !cached.timestamp) return null;
    
    const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
    if (isExpired) {
      cache.delete(configId);
      return null;
    }
    
    return cached;
  }, []);

  const setCachedData = useCallback((configId: string, data: Partial<CachedData>) => {
    const existing = cache.get(configId) || {};
    cache.set(configId, {
      ...existing,
      ...data,
      timestamp: Date.now(),
    });
  }, []);

  const prefetchManageKeysData = useCallback(async (configId: string) => {
    try {
      // This would typically prefetch data for the manage keys page
      // For now, we'll just return a promise that resolves
      return Promise.resolve();
    } catch (error) {
      console.error('Error prefetching manage keys data:', error);
    }
  }, []);

  const createHoverPrefetch = useCallback((configId: string) => {
    return {
      onMouseEnter: () => prefetchManageKeysData(configId),
    };
  }, [prefetchManageKeysData]);

  return {
    getCachedData,
    setCachedData,
    prefetchManageKeysData,
    createHoverPrefetch,
  };
}

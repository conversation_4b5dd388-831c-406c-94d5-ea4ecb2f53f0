#!/usr/bin/env node

/**
 * Test script for the API Key Generation System
 * This script tests the complete flow of API key generation and usage
 */

const https = require('https');
const crypto = require('crypto');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
const TEST_CONFIG_ID = process.env.TEST_CONFIG_ID; // Should be set to a real config ID

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testDatabaseConnection() {
  log('\n🔍 Testing Database Connection...', 'blue');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/user/profile',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, you'd need proper authentication
      }
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 401) {
      log('✅ Database connection working (authentication required as expected)', 'green');
      return true;
    } else if (response.statusCode === 200) {
      log('✅ Database connection working', 'green');
      return true;
    } else {
      log(`❌ Unexpected response: ${response.statusCode}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Database connection failed: ${error.message}`, 'red');
    return false;
  }
}

async function testApiKeyGeneration() {
  log('\n🔑 Testing API Key Generation...', 'blue');
  
  if (!TEST_CONFIG_ID) {
    log('⚠️  TEST_CONFIG_ID not set, skipping API key generation test', 'yellow');
    return null;
  }

  try {
    const testApiKeyData = {
      custom_api_config_id: TEST_CONFIG_ID,
      key_name: `Test Key ${Date.now()}`,
      permissions: {
        chat: true,
        streaming: true,
        all_models: true
      },
      rate_limit_per_minute: 30,
      rate_limit_per_hour: 500,
      rate_limit_per_day: 5000
    };

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/user-api-keys',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, you'd need proper authentication
      }
    };

    const response = await makeRequest(options, testApiKeyData);
    
    if (response.statusCode === 401) {
      log('✅ API key generation endpoint working (authentication required)', 'green');
      return null;
    } else if (response.statusCode === 201) {
      log('✅ API key generated successfully', 'green');
      log(`   Key prefix: ${response.body.key_prefix}`, 'blue');
      return response.body;
    } else {
      log(`❌ API key generation failed: ${response.statusCode}`, 'red');
      log(`   Error: ${JSON.stringify(response.body)}`, 'red');
      return null;
    }
  } catch (error) {
    log(`❌ API key generation test failed: ${error.message}`, 'red');
    return null;
  }
}

async function testOpenAICompatibleEndpoint(apiKey = null) {
  log('\n🌐 Testing OpenAI-Compatible Endpoint...', 'blue');
  
  const testMessage = {
    messages: [
      { role: 'user', content: 'Hello, this is a test message.' }
    ],
    stream: false
  };

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/openai-compatible/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  if (apiKey) {
    options.headers['Authorization'] = `Bearer ${apiKey}`;
  } else {
    options.headers['Authorization'] = 'Bearer test_key_for_format_validation';
  }

  try {
    const response = await makeRequest(options, testMessage);
    
    if (response.statusCode === 401) {
      log('✅ OpenAI-compatible endpoint working (authentication required)', 'green');
      return true;
    } else if (response.statusCode === 200) {
      log('✅ OpenAI-compatible endpoint working', 'green');
      return true;
    } else {
      log(`❌ OpenAI-compatible endpoint failed: ${response.statusCode}`, 'red');
      log(`   Error: ${JSON.stringify(response.body)}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ OpenAI-compatible endpoint test failed: ${error.message}`, 'red');
    return false;
  }
}

async function testApiKeyValidation() {
  log('\n🔐 Testing API Key Validation...', 'blue');
  
  // Test with invalid API key format
  const invalidKeys = [
    'invalid_key',
    'rk_test_invalid',
    'sk-invalid-openai-key',
    ''
  ];

  let validationWorking = true;

  for (const invalidKey of invalidKeys) {
    try {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/v1/openai-compatible/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${invalidKey}`
        }
      };

      const response = await makeRequest(options, {
        messages: [{ role: 'user', content: 'test' }]
      });

      if (response.statusCode !== 401) {
        log(`❌ Invalid key "${invalidKey}" was not rejected (status: ${response.statusCode})`, 'red');
        validationWorking = false;
      }
    } catch (error) {
      // Network errors are expected for invalid requests
    }
  }

  if (validationWorking) {
    log('✅ API key validation working correctly', 'green');
  }

  return validationWorking;
}

async function testRateLimiting() {
  log('\n⏱️  Testing Rate Limiting Headers...', 'blue');
  
  try {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/openai-compatible/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_key'
      }
    };

    const response = await makeRequest(options, {
      messages: [{ role: 'user', content: 'test' }]
    });

    // Check if rate limiting headers are present (even in error responses)
    const hasRateLimitHeaders = 
      response.headers['x-ratelimit-limit-minute'] ||
      response.headers['X-RateLimit-Limit-Minute'];

    if (hasRateLimitHeaders) {
      log('✅ Rate limiting headers present', 'green');
      return true;
    } else {
      log('⚠️  Rate limiting headers not found (may be normal for invalid keys)', 'yellow');
      return true; // Not a failure
    }
  } catch (error) {
    log(`❌ Rate limiting test failed: ${error.message}`, 'red');
    return false;
  }
}

async function runTests() {
  log(`${colors.bold}🚀 RouKey API Key System Integration Test${colors.reset}`, 'blue');
  log(`Testing against: ${BASE_URL}`, 'blue');
  
  const results = {
    database: false,
    apiKeyGeneration: false,
    openaiEndpoint: false,
    validation: false,
    rateLimiting: false
  };

  // Run tests
  results.database = await testDatabaseConnection();
  results.apiKeyGeneration = await testApiKeyGeneration() !== null;
  results.openaiEndpoint = await testOpenAICompatibleEndpoint();
  results.validation = await testApiKeyValidation();
  results.rateLimiting = await testRateLimiting();

  // Summary
  log('\n📊 Test Results Summary:', 'bold');
  log('========================', 'bold');
  
  const tests = [
    { name: 'Database Connection', result: results.database },
    { name: 'API Key Generation', result: results.apiKeyGeneration },
    { name: 'OpenAI-Compatible Endpoint', result: results.openaiEndpoint },
    { name: 'API Key Validation', result: results.validation },
    { name: 'Rate Limiting', result: results.rateLimiting }
  ];

  let passedTests = 0;
  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    const color = test.result ? 'green' : 'red';
    log(`${test.name.padEnd(30)} ${status}`, color);
    if (test.result) passedTests++;
  });

  log('\n' + '='.repeat(50), 'bold');
  log(`Overall: ${passedTests}/${tests.length} tests passed`, passedTests === tests.length ? 'green' : 'yellow');

  if (passedTests === tests.length) {
    log('\n🎉 All tests passed! The API key system is ready for use.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please check the implementation.', 'yellow');
  }

  log('\n📝 Next Steps:', 'blue');
  log('1. Set up proper authentication for full testing', 'blue');
  log('2. Create a test configuration ID and set TEST_CONFIG_ID', 'blue');
  log('3. Test with real user authentication', 'blue');
  log('4. Verify the UI components work in the browser', 'blue');
}

// Run the tests
if (require.main === module) {
  runTests().catch(error => {
    log(`\n💥 Test runner failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runTests };

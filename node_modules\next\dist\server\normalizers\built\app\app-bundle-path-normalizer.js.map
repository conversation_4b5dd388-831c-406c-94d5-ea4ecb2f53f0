{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-bundle-path-normalizer.ts"], "sourcesContent": ["import { Normalizers } from '../../normalizers'\nimport type { Normalizer } from '../../normalizer'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\n\nexport class AppBundlePathNormalizer extends PrefixingNormalizer {\n  constructor() {\n    super('app')\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(normalizePagePath(page))\n  }\n}\n\nexport class DevAppBundlePathNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppBundlePathNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["AppBundlePathNormalizer", "DevAppBundlePathNormalizer", "PrefixingNormalizer", "constructor", "normalize", "page", "normalizePagePath", "Normalizers", "pageNormalizer", "filename"], "mappings": ";;;;;;;;;;;;;;;IAKaA,uBAAuB;eAAvBA;;IAUAC,0BAA0B;eAA1BA;;;6BAfe;qCAEQ;mCACF;AAE3B,MAAMD,gCAAgCE,wCAAmB;IAC9DC,aAAc;QACZ,KAAK,CAAC;IACR;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUE,IAAAA,oCAAiB,EAACD;IAC3C;AACF;AAEO,MAAMJ,mCAAmCM,wBAAW;IACzDJ,YAAYK,cAA0B,CAAE;QACtC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIR;SACL;IACH;IAEOI,UAAUK,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACL,UAAUK;IACzB;AACF"}
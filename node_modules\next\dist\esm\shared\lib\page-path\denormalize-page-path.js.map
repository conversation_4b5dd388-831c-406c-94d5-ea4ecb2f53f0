{"version": 3, "sources": ["../../../../src/shared/lib/page-path/denormalize-page-path.ts"], "sourcesContent": ["import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n"], "names": ["isDynamicRoute", "normalizePathSep", "denormalizePagePath", "page", "_page", "startsWith", "slice"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAiB;AAChD,SAASC,gBAAgB,QAAQ,uBAAsB;AAEvD;;;;;;;CAOC,GACD,OAAO,SAASC,oBAAoBC,IAAY;IAC9C,IAAIC,QAAQH,iBAAiBE;IAC7B,OAAOC,MAAMC,UAAU,CAAC,cAAc,CAACL,eAAeI,SAClDA,MAAME,KAAK,CAAC,KACZF,UAAU,WACRA,QACA;AACR"}
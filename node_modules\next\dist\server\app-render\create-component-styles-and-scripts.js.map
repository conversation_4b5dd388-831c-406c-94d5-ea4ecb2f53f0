{"version": 3, "sources": ["../../../src/server/app-render/create-component-styles-and-scripts.tsx"], "sourcesContent": ["import React from 'react'\nimport { interopDefault } from './interop-default'\nimport { getLinkAndScriptTags } from './get-css-inlined-link-tags'\nimport type { AppRenderContext } from './app-render'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport { renderCssResource } from './render-css-resource'\n\nexport async function createComponentStylesAndScripts({\n  filePath,\n  getComponent,\n  injectedCSS,\n  injectedJS,\n  ctx,\n}: {\n  filePath: string\n  getComponent: () => any\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  ctx: AppRenderContext\n}): Promise<[React.ComponentType<any>, React.ReactNode, React.ReactNode]> {\n  const { styles: entryCssFiles, scripts: jsHrefs } = getLinkAndScriptTags(\n    ctx.clientReferenceManifest,\n    filePath,\n    injectedCSS,\n    injectedJS\n  )\n\n  const styles = renderCssResource(entryCssFiles, ctx)\n\n  const scripts = jsHrefs\n    ? jsHrefs.map((href, index) => (\n        <script\n          src={`${ctx.assetPrefix}/_next/${encodeURIPath(\n            href\n          )}${getAssetQueryString(ctx, true)}`}\n          async={true}\n          key={`script-${index}`}\n        />\n      ))\n    : null\n\n  const Comp = interopDefault(await getComponent())\n\n  return [Comp, styles, scripts]\n}\n"], "names": ["createComponentStylesAndScripts", "filePath", "getComponent", "injectedCSS", "injectedJS", "ctx", "styles", "entryCssFiles", "scripts", "jsHrefs", "getLinkAndScriptTags", "clientReferenceManifest", "renderCssResource", "map", "href", "index", "script", "src", "assetPrefix", "encodeURIPath", "getAssetQueryString", "async", "Comp", "interopDefault"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;8DARJ;gCACa;uCACM;qCAED;+BACN;mCACI;;;;;;AAE3B,eAAeA,gCAAgC,EACpDC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,GAAG,EAOJ;IACC,MAAM,EAAEC,QAAQC,aAAa,EAAEC,SAASC,OAAO,EAAE,GAAGC,IAAAA,2CAAoB,EACtEL,IAAIM,uBAAuB,EAC3BV,UACAE,aACAC;IAGF,MAAME,SAASM,IAAAA,oCAAiB,EAACL,eAAeF;IAEhD,MAAMG,UAAUC,UACZA,QAAQI,GAAG,CAAC,CAACC,MAAMC,sBACjB,qBAACC;YACCC,KAAK,GAAGZ,IAAIa,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EAC5CL,QACEM,IAAAA,wCAAmB,EAACf,KAAK,OAAO;YACpCgB,OAAO;WACF,CAAC,OAAO,EAAEN,OAAO,KAG1B;IAEJ,MAAMO,OAAOC,IAAAA,8BAAc,EAAC,MAAMrB;IAElC,OAAO;QAACoB;QAAMhB;QAAQE;KAAQ;AAChC"}
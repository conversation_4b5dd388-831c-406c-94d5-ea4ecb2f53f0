{"version": 3, "sources": ["../../src/client/add-locale.ts"], "sourcesContent": ["import type { addLocale as Fn } from '../shared/lib/router/utils/add-locale'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nexport const addLocale: typeof Fn = (path, ...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return normalizePathTrailingSlash(\n      require('../shared/lib/router/utils/add-locale').addLocale(path, ...args)\n    )\n  }\n  return path\n}\n"], "names": ["normalizePathTrailingSlash", "addLocale", "path", "args", "process", "env", "__NEXT_I18N_SUPPORT", "require"], "mappings": "AACA,SAASA,0BAA0B,QAAQ,6BAA4B;AAEvE,OAAO,MAAMC,YAAuB,SAACC;qCAASC;QAAAA;;IAC5C,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,OAAON,2BACLO,QAAQ,yCAAyCN,SAAS,CAACC,SAASC;IAExE;IACA,OAAOD;AACT,EAAC"}
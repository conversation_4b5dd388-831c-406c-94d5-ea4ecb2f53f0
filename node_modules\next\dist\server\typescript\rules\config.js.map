{"version": 3, "sources": ["../../../../src/server/typescript/rules/config.ts"], "sourcesContent": ["// This module provides intellisense for page and layout's exported configs.\n\nimport {\n  getSource,\n  isPositionInsideNode,\n  getTs,\n  removeStringQuotes,\n} from '../utils'\nimport {\n  NEXT_TS_ERRORS,\n  ALLOWED_EXPORTS,\n  LEGACY_CONFIG_EXPORT,\n} from '../constant'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst API_DOCS: Record<\n  string,\n  {\n    description: string\n    options?: Record<string, string>\n    link?: string\n    type?: string\n    isValid?: (value: string) => boolean\n    getHint?: (value: any) => string | undefined\n  }\n> = {\n  dynamic: {\n    description:\n      'The `dynamic` option provides a few ways to opt in or out of dynamic behavior.',\n    options: {\n      '\"auto\"':\n        'Heuristic to cache as much as possible but doesn’t prevent any component to opt-in to dynamic behavior.',\n      '\"force-dynamic\"':\n        'This disables all caching of fetches and always revalidates. (This is equivalent to `getServerSideProps`.)',\n      '\"error\"':\n        'This errors if any dynamic Hooks or fetches are used. (This is equivalent to `getStaticProps`.)',\n      '\"force-static\"':\n        'This forces caching of all fetches and returns empty values from `cookies`, `headers` and `useSearchParams`.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamic',\n  },\n  fetchCache: {\n    description:\n      'The `fetchCache` option controls how Next.js statically caches fetches. By default it statically caches fetches reachable before any dynamic Hooks are used, and it doesn’t cache fetches that are discovered after that.',\n    options: {\n      '\"force-no-store\"':\n        \"This lets you intentionally opt-out of all caching of data. This option forces all fetches to be refetched every request even if the `cache: 'force-cache'` option is passed to `fetch()`.\",\n      '\"only-no-store\"':\n        \"This lets you enforce that all data opts out of caching. This option makes `fetch()` reject with an error if `cache: 'force-cache'` is provided. It also changes the default to `no-store`.\",\n      '\"default-no-store\"':\n        \"Allows any explicit `cache` option to be passed to `fetch()` but if `'default'`, or no option, is provided then it defaults to `'no-store'`. This means that even fetches before a dynamic Hook are considered dynamic.\",\n      '\"auto\"':\n        'This is the default option. It caches any fetches with the default `cache` option provided, that happened before a dynamic Hook is used and don’t cache any such fetches if they’re issued after a dynamic Hook.',\n      '\"default-cache\"':\n        \"Allows any explicit `cache` option to be passed to `fetch()` but if `'default'`, or no option, is provided then it defaults to `'force-cache'`. This means that even fetches before a dynamic Hook are considered dynamic.\",\n      '\"only-cache\"':\n        \"This lets you enforce that all data opts into caching. This option makes `fetch()` reject with an error if `cache: 'force-cache'` is provided. It also changes the default to `force-cache`. This error can be discovered early during static builds - or dynamically during Edge rendering.\",\n      '\"force-cache\"':\n        \"This lets you intentionally opt-in to all caching of data. This option forces all fetches to be cache even if the `cache: 'no-store'` option is passed to `fetch()`.\",\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#fetchcache',\n  },\n  preferredRegion: {\n    description:\n      'Specify the perferred region that this layout or page should be deployed to. If the region option is not specified, it inherits the option from the nearest parent layout. The root defaults to `\"auto\"`.\\n\\nYou can also specify a region, such as \"iad1\", or an array of regions, such as `[\"iad1\", \"sfo1\"]`.',\n    options: {\n      '\"auto\"':\n        'Next.js will first deploy to the `\"home\"` region. Then if it doesn’t detect any waterfall requests after a few requests, it can upgrade that route, to be deployed globally. If it detects any waterfall requests after that, it can eventually downgrade back to `\"home`\".',\n      '\"global\"': 'Prefer deploying globally.',\n      '\"home\"': 'Prefer deploying to the Home region.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#preferredregion',\n    isValid: (value: string) => {\n      try {\n        const parsed = JSON.parse(value)\n        return (\n          typeof parsed === 'string' ||\n          (Array.isArray(parsed) && !parsed.some((v) => typeof v !== 'string'))\n        )\n      } catch (err) {\n        return false\n      }\n    },\n    getHint: (value: any) => {\n      if (value === 'auto') return `Automatically chosen by Next.js.`\n      if (value === 'global') return `Prefer deploying globally.`\n      if (value === 'home') return `Prefer deploying to the Home region.`\n      if (Array.isArray(value)) return `Deploy to regions: ${value.join(', ')}.`\n      if (typeof value === 'string') return `Deploy to region: ${value}.`\n    },\n  },\n  revalidate: {\n    description:\n      'The `revalidate` option sets the default revalidation time for that layout or page. Note that it doesn’t override the value specify by each `fetch()`.',\n    type: 'mixed',\n    options: {\n      false:\n        'This is the default and changes the fetch cache to indefinitely cache anything that uses force-cache or is fetched before a dynamic Hook/fetch.',\n      0: 'Specifying `0` implies that this layout or page should never be static.',\n      30: 'Set the revalidation time to `30` seconds. The value can be `0` or any positive number.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#revalidate',\n    isValid: (value: string) => {\n      return value === 'false' || Number(value.replace(/_/g, '')) >= 0\n    },\n    getHint: (value: any) => {\n      return `Set the default revalidation time to \\`${value}\\` seconds.`\n    },\n  },\n  dynamicParams: {\n    description:\n      '`dynamicParams` replaces the `fallback` option of `getStaticPaths`. It controls whether we allow `dynamicParams` beyond the generated static params from `generateStaticParams`.',\n    options: {\n      true: 'Allow rendering dynamic params that are not generated by `generateStaticParams`.',\n      false:\n        'Disallow rendering dynamic params that are not generated by `generateStaticParams`.',\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamicparams',\n    isValid: (value: string) => {\n      return value === 'true' || value === 'false'\n    },\n  },\n  runtime: {\n    description:\n      'The `runtime` option controls the preferred runtime to render this route.',\n    options: {\n      '\"nodejs\"': 'Prefer the Node.js runtime.',\n      '\"edge\"': 'Prefer the Edge runtime.',\n      '\"experimental-edge\"': `@deprecated\\n\\nThis option is no longer experimental. Use \\`edge\\` instead.`,\n    },\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#runtime',\n  },\n  metadata: {\n    description: 'Next.js Metadata configurations',\n    link: 'https://nextjs.org/docs/app/building-your-application/optimizing/metadata',\n  },\n  maxDuration: {\n    description:\n      '`maxDuration` allows you to set max default execution time for your function. If it is not specified, the default value is dependent on your deployment platform and plan.',\n    link: 'https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#maxduration',\n  },\n  experimental_ppr: {\n    description: `Enables experimental Partial Prerendering (PPR) for this page / layout, when PPR is set to \"incremental\" mode.`,\n    link: 'https://nextjs.org/docs/app/api-reference/next-config-js/ppr',\n    options: {\n      true: 'Enable PPR for this route',\n      false: 'Disable PPR for this route',\n    },\n    isValid: (value: string) => {\n      return value === 'true' || value === 'false'\n    },\n  },\n}\n\nfunction visitEntryConfig(\n  fileName: string,\n  position: number,\n  callback: (entryEonfig: string, value: tsModule.VariableDeclaration) => void\n) {\n  const source = getSource(fileName)\n  if (source) {\n    const ts = getTs()\n    ts.forEachChild(source, function visit(node) {\n      // Covered by this node\n      if (isPositionInsideNode(position, node)) {\n        // Export variable\n        if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          if (ts.isVariableDeclarationList(node.declarationList)) {\n            for (const declaration of node.declarationList.declarations) {\n              if (isPositionInsideNode(position, declaration)) {\n                // `export const ... = ...`\n                const text = declaration.name.getText()\n                callback(text, declaration)\n              }\n            }\n          }\n        }\n      }\n    })\n  }\n}\n\nfunction createAutoCompletionOptionName(sort: number, name: string) {\n  const ts = getTs()\n  return {\n    name,\n    sortText: '!' + sort,\n    kind: ts.ScriptElementKind.constElement,\n    kindModifiers: ts.ScriptElementKindModifier.exportedModifier,\n    labelDetails: {\n      description: `Next.js ${name} option`,\n    },\n    data: {\n      exportName: name,\n      moduleSpecifier: 'next/typescript/entry_option_name',\n    },\n  } as tsModule.CompletionEntry\n}\n\nfunction createAutoCompletionOptionValue(\n  sort: number,\n  name: string,\n  apiName: string\n) {\n  const ts = getTs()\n  const isString = name.startsWith('\"')\n  return {\n    name,\n    insertText: removeStringQuotes(name),\n    sortText: '' + sort,\n    kind: isString ? ts.ScriptElementKind.string : ts.ScriptElementKind.unknown,\n    kindModifiers: ts.ScriptElementKindModifier.none,\n    labelDetails: {\n      description: `Next.js ${apiName} option`,\n    },\n    data: {\n      exportName: apiName,\n      moduleSpecifier: 'next/typescript/entry_option_value',\n    },\n  } as tsModule.CompletionEntry\n}\n\nfunction getAPIDescription(api: string): string {\n  return (\n    API_DOCS[api].description +\n    '\\n\\n' +\n    Object.entries(API_DOCS[api].options || {})\n      .map(([key, value]) => `- \\`${key}\\`: ${value}`)\n      .join('\\n')\n  )\n}\nconst config = {\n  // Auto completion for entry exported configs.\n  addCompletionsAtPosition(\n    fileName: string,\n    position: number,\n    prior: tsModule.WithMetadata<tsModule.CompletionInfo>\n  ) {\n    visitEntryConfig(fileName, position, (entryConfig, declaration) => {\n      if (!API_DOCS[entryConfig]) {\n        if (isPositionInsideNode(position, declaration.name)) {\n          prior.entries.push(\n            ...Object.keys(API_DOCS).map((name, index) => {\n              return createAutoCompletionOptionName(index, name)\n            })\n          )\n        }\n        return\n      }\n\n      prior.entries.push(\n        ...Object.keys(API_DOCS[entryConfig].options || {}).map(\n          (name, index) => {\n            return createAutoCompletionOptionValue(index, name, entryConfig)\n          }\n        )\n      )\n    })\n  },\n\n  // Show docs when hovering on the exported configs.\n  getQuickInfoAtPosition(fileName: string, position: number) {\n    const ts = getTs()\n\n    let overridden: tsModule.QuickInfo | undefined\n    visitEntryConfig(fileName, position, (entryConfig, declaration) => {\n      if (!API_DOCS[entryConfig]) return\n\n      const name = declaration.name\n      const value = declaration.initializer\n\n      const docsLink = {\n        kind: 'text',\n        text:\n          `\\n\\nRead more about the \"${entryConfig}\" option: ` +\n          API_DOCS[entryConfig].link,\n      }\n\n      if (value && isPositionInsideNode(position, value)) {\n        // Hovers the value of the config\n        const isString = ts.isStringLiteral(value)\n        const text = removeStringQuotes(value.getText())\n        const key = isString ? `\"${text}\"` : text\n\n        const isValid = API_DOCS[entryConfig].isValid\n          ? API_DOCS[entryConfig].isValid?.(key)\n          : !!API_DOCS[entryConfig].options?.[key]\n\n        if (isValid) {\n          overridden = {\n            kind: ts.ScriptElementKind.enumElement,\n            kindModifiers: ts.ScriptElementKindModifier.none,\n            textSpan: {\n              start: value.getStart(),\n              length: value.getWidth(),\n            },\n            displayParts: [],\n            documentation: [\n              {\n                kind: 'text',\n                text:\n                  API_DOCS[entryConfig].options?.[key] ||\n                  API_DOCS[entryConfig].getHint?.(key) ||\n                  '',\n              },\n              docsLink,\n            ],\n          }\n        } else {\n          // Wrong value, display the docs link\n          overridden = {\n            kind: ts.ScriptElementKind.enumElement,\n            kindModifiers: ts.ScriptElementKindModifier.none,\n            textSpan: {\n              start: value.getStart(),\n              length: value.getWidth(),\n            },\n            displayParts: [],\n            documentation: [docsLink],\n          }\n        }\n      } else {\n        // Hovers the name of the config\n        overridden = {\n          kind: ts.ScriptElementKind.enumElement,\n          kindModifiers: ts.ScriptElementKindModifier.none,\n          textSpan: {\n            start: name.getStart(),\n            length: name.getWidth(),\n          },\n          displayParts: [],\n          documentation: [\n            {\n              kind: 'text',\n              text: getAPIDescription(entryConfig),\n            },\n            docsLink,\n          ],\n        }\n      }\n    })\n    return overridden\n  },\n\n  // Show details on the side when auto completing.\n  getCompletionEntryDetails(\n    entryName: string,\n    data: tsModule.CompletionEntryData\n  ) {\n    const ts = getTs()\n    if (\n      data &&\n      data.moduleSpecifier &&\n      data.moduleSpecifier.startsWith('next/typescript')\n    ) {\n      let content = ''\n      if (data.moduleSpecifier === 'next/typescript/entry_option_name') {\n        content = getAPIDescription(entryName)\n      } else {\n        const options = API_DOCS[data.exportName].options\n        if (!options) return\n        content = options[entryName]\n      }\n      return {\n        name: entryName,\n        kind: ts.ScriptElementKind.enumElement,\n        kindModifiers: ts.ScriptElementKindModifier.none,\n        displayParts: [],\n        documentation: [\n          {\n            kind: 'text',\n            text: content,\n          },\n        ],\n      }\n    }\n  },\n\n  // Show errors for invalid export fields.\n  getSemanticDiagnosticsForExportVariableStatement(\n    source: tsModule.SourceFile,\n    node: tsModule.VariableStatement\n  ) {\n    const ts = getTs()\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    // Check if it has correct option exports\n    if (ts.isVariableDeclarationList(node.declarationList)) {\n      for (const declaration of node.declarationList.declarations) {\n        const name = declaration.name\n        if (ts.isIdentifier(name)) {\n          if (!ALLOWED_EXPORTS.includes(name.text) && !API_DOCS[name.text]) {\n            diagnostics.push({\n              file: source,\n              category: ts.DiagnosticCategory.Error,\n              code: NEXT_TS_ERRORS.INVALID_ENTRY_EXPORT,\n              messageText: `\"${name.text}\" is not a valid Next.js entry export value.`,\n              start: name.getStart(),\n              length: name.getWidth(),\n            })\n          } else if (API_DOCS[name.text]) {\n            // Check if the value is valid\n            const value = declaration.initializer\n            const options = API_DOCS[name.text].options\n\n            if (value && options) {\n              let displayedValue = ''\n              let errorMessage = ''\n              let isInvalid = false\n\n              if (\n                ts.isStringLiteral(value) ||\n                ts.isNoSubstitutionTemplateLiteral(value)\n              ) {\n                const val = '\"' + removeStringQuotes(value.getText()) + '\"'\n                const allowedValues = Object.keys(options).filter((v) =>\n                  /^['\"]/.test(v)\n                )\n\n                if (\n                  !allowedValues.includes(val) &&\n                  !API_DOCS[name.text].isValid?.(val)\n                ) {\n                  isInvalid = true\n                  displayedValue = val\n                }\n              } else if (\n                ts.isNumericLiteral(value) ||\n                (ts.isPrefixUnaryExpression(value) &&\n                  ts.isMinusToken((value as any).operator) &&\n                  (ts.isNumericLiteral((value as any).operand.kind) ||\n                    (ts.isIdentifier((value as any).operand.kind) &&\n                      (value as any).operand.kind.getText() === 'Infinity'))) ||\n                (ts.isIdentifier(value) && value.getText() === 'Infinity')\n              ) {\n                const v = value.getText()\n                if (!API_DOCS[name.text].isValid?.(v)) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (\n                value.kind === ts.SyntaxKind.TrueKeyword ||\n                value.kind === ts.SyntaxKind.FalseKeyword\n              ) {\n                const v = value.getText()\n                if (!API_DOCS[name.text].isValid?.(v)) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (ts.isArrayLiteralExpression(value)) {\n                const v = value.getText()\n                if (\n                  !API_DOCS[name.text].isValid?.(\n                    JSON.stringify(value.elements.map((e) => e.getText()))\n                  )\n                ) {\n                  isInvalid = true\n                  displayedValue = v\n                }\n              } else if (\n                // Other literals\n                ts.isBigIntLiteral(value) ||\n                ts.isObjectLiteralExpression(value) ||\n                ts.isRegularExpressionLiteral(value) ||\n                ts.isPrefixUnaryExpression(value)\n              ) {\n                isInvalid = true\n                displayedValue = value.getText()\n              } else {\n                // Not a literal, error because it's not statically analyzable\n                isInvalid = true\n                displayedValue = value.getText()\n                errorMessage = `\"${displayedValue}\" is not a valid value for the \"${name.text}\" option. The configuration must be statically analyzable.`\n              }\n\n              if (isInvalid) {\n                diagnostics.push({\n                  file: source,\n                  category: ts.DiagnosticCategory.Error,\n                  code: NEXT_TS_ERRORS.INVALID_OPTION_VALUE,\n                  messageText:\n                    errorMessage ||\n                    `\"${displayedValue}\" is not a valid value for the \"${name.text}\" option.`,\n                  start: value.getStart(),\n                  length: value.getWidth(),\n                })\n              }\n            }\n          } else if (name.text === LEGACY_CONFIG_EXPORT) {\n            // export const config = { ... }\n            // Error if using `amp: ...`\n            const value = declaration.initializer\n            if (value && ts.isObjectLiteralExpression(value)) {\n              for (const prop of value.properties) {\n                if (\n                  ts.isPropertyAssignment(prop) &&\n                  ts.isIdentifier(prop.name) &&\n                  prop.name.text === 'amp'\n                ) {\n                  diagnostics.push({\n                    file: source,\n                    category: ts.DiagnosticCategory.Error,\n                    code: NEXT_TS_ERRORS.INVALID_CONFIG_OPTION,\n                    messageText: `AMP is not supported in the app directory. If you need to use AMP it will continue to be supported in the pages directory.`,\n                    start: prop.getStart(),\n                    length: prop.getWidth(),\n                  })\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default config\n"], "names": ["API_DOCS", "dynamic", "description", "options", "link", "fetchCache", "preferredRegion", "<PERSON><PERSON><PERSON><PERSON>", "value", "parsed", "JSON", "parse", "Array", "isArray", "some", "v", "err", "getHint", "join", "revalidate", "type", "false", "Number", "replace", "dynamicParams", "true", "runtime", "metadata", "maxDuration", "experimental_ppr", "visitEntryConfig", "fileName", "position", "callback", "source", "getSource", "ts", "getTs", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isPositionInsideNode", "isVariableStatement", "modifiers", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "text", "name", "getText", "createAutoCompletionOptionName", "sort", "sortText", "ScriptElementKind", "constElement", "kindModifiers", "ScriptElementKindModifier", "exportedModifier", "labelDetails", "data", "exportName", "moduleSpecifier", "createAutoCompletionOptionValue", "apiName", "isString", "startsWith", "insertText", "removeStringQuotes", "string", "unknown", "none", "getAPIDescription", "api", "Object", "entries", "map", "key", "config", "addCompletionsAtPosition", "prior", "entryConfig", "push", "keys", "index", "getQuickInfoAtPosition", "overridden", "initializer", "docsLink", "isStringLiteral", "enumElement", "textSpan", "start", "getStart", "length", "getWidth", "displayParts", "documentation", "getCompletionEntryDetails", "entryName", "content", "getSemanticDiagnosticsForExportVariableStatement", "diagnostics", "isIdentifier", "ALLOWED_EXPORTS", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_ENTRY_EXPORT", "messageText", "displayedValue", "errorMessage", "isInvalid", "isNoSubstitutionTemplateLiteral", "val", "<PERSON><PERSON><PERSON><PERSON>", "filter", "test", "isNumericLiteral", "isPrefixUnaryExpression", "isMinusToken", "operator", "operand", "TrueKeyword", "FalseKeyword", "isArrayLiteralExpression", "stringify", "elements", "e", "isBigIntLiteral", "isObjectLiteralExpression", "isRegularExpressionLiteral", "INVALID_OPTION_VALUE", "LEGACY_CONFIG_EXPORT", "prop", "properties", "isPropertyAssignment", "INVALID_CONFIG_OPTION"], "mappings": "AAAA,4EAA4E;;;;;+BA2gB5E;;;eAAA;;;uBApgBO;0BAKA;AAGP,MAAMA,WAUF;IACFC,SAAS;QACPC,aACE;QACFC,SAAS;YACP,UACE;YACF,mBACE;YACF,WACE;YACF,kBACE;QACJ;QACAC,MAAM;IACR;IACAC,YAAY;QACVH,aACE;QACFC,SAAS;YACP,oBACE;YACF,mBACE;YACF,sBACE;YACF,UACE;YACF,mBACE;YACF,gBACE;YACF,iBACE;QACJ;QACAC,MAAM;IACR;IACAE,iBAAiB;QACfJ,aACE;QACFC,SAAS;YACP,UACE;YACF,YAAY;YACZ,UAAU;QACZ;QACAC,MAAM;QACNG,SAAS,CAACC;YACR,IAAI;gBACF,MAAMC,SAASC,KAAKC,KAAK,CAACH;gBAC1B,OACE,OAAOC,WAAW,YACjBG,MAAMC,OAAO,CAACJ,WAAW,CAACA,OAAOK,IAAI,CAAC,CAACC,IAAM,OAAOA,MAAM;YAE/D,EAAE,OAAOC,KAAK;gBACZ,OAAO;YACT;QACF;QACAC,SAAS,CAACT;YACR,IAAIA,UAAU,QAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC/D,IAAIA,UAAU,UAAU,OAAO,CAAC,0BAA0B,CAAC;YAC3D,IAAIA,UAAU,QAAQ,OAAO,CAAC,oCAAoC,CAAC;YACnE,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAO,CAAC,mBAAmB,EAAEA,MAAMU,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,OAAOV,UAAU,UAAU,OAAO,CAAC,kBAAkB,EAAEA,MAAM,CAAC,CAAC;QACrE;IACF;IACAW,YAAY;QACVjB,aACE;QACFkB,MAAM;QACNjB,SAAS;YACPkB,OACE;YACF,GAAG;YACH,IAAI;QACN;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,WAAWc,OAAOd,MAAMe,OAAO,CAAC,MAAM,QAAQ;QACjE;QACAN,SAAS,CAACT;YACR,OAAO,CAAC,uCAAuC,EAAEA,MAAM,WAAW,CAAC;QACrE;IACF;IACAgB,eAAe;QACbtB,aACE;QACFC,SAAS;YACPsB,MAAM;YACNJ,OACE;QACJ;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;IACAkB,SAAS;QACPxB,aACE;QACFC,SAAS;YACP,YAAY;YACZ,UAAU;YACV,uBAAuB,CAAC,2EAA2E,CAAC;QACtG;QACAC,MAAM;IACR;IACAuB,UAAU;QACRzB,aAAa;QACbE,MAAM;IACR;IACAwB,aAAa;QACX1B,aACE;QACFE,MAAM;IACR;IACAyB,kBAAkB;QAChB3B,aAAa,CAAC,8GAA8G,CAAC;QAC7HE,MAAM;QACND,SAAS;YACPsB,MAAM;YACNJ,OAAO;QACT;QACAd,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;AACF;AAEA,SAASsB,iBACPC,QAAgB,EAChBC,QAAgB,EAChBC,QAA4E;IAE5E,MAAMC,SAASC,IAAAA,gBAAS,EAACJ;IACzB,IAAIG,QAAQ;QACV,MAAME,KAAKC,IAAAA,YAAK;QAChBD,GAAGE,YAAY,CAACJ,QAAQ,SAASK,MAAMC,IAAI;YACzC,uBAAuB;YACvB,IAAIC,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEJ,GAAGM,mBAAmB,CAACF,WACvBA,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgB1B,IAAI,CAAC,CAAC8B,IAAMA,EAAEC,IAAI,KAAKT,GAAGU,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIX,GAAGY,yBAAyB,CAACR,KAAKS,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;4BAC3D,IAAIV,IAAAA,2BAAoB,EAACT,UAAUkB,cAAc;gCAC/C,2BAA2B;gCAC3B,MAAME,OAAOF,YAAYG,IAAI,CAACC,OAAO;gCACrCrB,SAASmB,MAAMF;4BACjB;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASK,+BAA+BC,IAAY,EAAEH,IAAY;IAChE,MAAMjB,KAAKC,IAAAA,YAAK;IAChB,OAAO;QACLgB;QACAI,UAAU,MAAMD;QAChBX,MAAMT,GAAGsB,iBAAiB,CAACC,YAAY;QACvCC,eAAexB,GAAGyB,yBAAyB,CAACC,gBAAgB;QAC5DC,cAAc;YACZ7D,aAAa,CAAC,QAAQ,EAAEmD,KAAK,OAAO,CAAC;QACvC;QACAW,MAAM;YACJC,YAAYZ;YACZa,iBAAiB;QACnB;IACF;AACF;AAEA,SAASC,gCACPX,IAAY,EACZH,IAAY,EACZe,OAAe;IAEf,MAAMhC,KAAKC,IAAAA,YAAK;IAChB,MAAMgC,WAAWhB,KAAKiB,UAAU,CAAC;IACjC,OAAO;QACLjB;QACAkB,YAAYC,IAAAA,yBAAkB,EAACnB;QAC/BI,UAAU,KAAKD;QACfX,MAAMwB,WAAWjC,GAAGsB,iBAAiB,CAACe,MAAM,GAAGrC,GAAGsB,iBAAiB,CAACgB,OAAO;QAC3Ed,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;QAChDZ,cAAc;YACZ7D,aAAa,CAAC,QAAQ,EAAEkE,QAAQ,OAAO,CAAC;QAC1C;QACAJ,MAAM;YACJC,YAAYG;YACZF,iBAAiB;QACnB;IACF;AACF;AAEA,SAASU,kBAAkBC,GAAW;IACpC,OACE7E,QAAQ,CAAC6E,IAAI,CAAC3E,WAAW,GACzB,SACA4E,OAAOC,OAAO,CAAC/E,QAAQ,CAAC6E,IAAI,CAAC1E,OAAO,IAAI,CAAC,GACtC6E,GAAG,CAAC,CAAC,CAACC,KAAKzE,MAAM,GAAK,CAAC,IAAI,EAAEyE,IAAI,IAAI,EAAEzE,OAAO,EAC9CU,IAAI,CAAC;AAEZ;AACA,MAAMgE,SAAS;IACb,8CAA8C;IAC9CC,0BACEpD,QAAgB,EAChBC,QAAgB,EAChBoD,KAAqD;QAErDtD,iBAAiBC,UAAUC,UAAU,CAACqD,aAAanC;YACjD,IAAI,CAAClD,QAAQ,CAACqF,YAAY,EAAE;gBAC1B,IAAI5C,IAAAA,2BAAoB,EAACT,UAAUkB,YAAYG,IAAI,GAAG;oBACpD+B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACvF,UAAUgF,GAAG,CAAC,CAAC3B,MAAMmC;wBAClC,OAAOjC,+BAA+BiC,OAAOnC;oBAC/C;gBAEJ;gBACA;YACF;YAEA+B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACvF,QAAQ,CAACqF,YAAY,CAAClF,OAAO,IAAI,CAAC,GAAG6E,GAAG,CACrD,CAAC3B,MAAMmC;gBACL,OAAOrB,gCAAgCqB,OAAOnC,MAAMgC;YACtD;QAGN;IACF;IAEA,mDAAmD;IACnDI,wBAAuB1D,QAAgB,EAAEC,QAAgB;QACvD,MAAMI,KAAKC,IAAAA,YAAK;QAEhB,IAAIqD;QACJ5D,iBAAiBC,UAAUC,UAAU,CAACqD,aAAanC;YACjD,IAAI,CAAClD,QAAQ,CAACqF,YAAY,EAAE;YAE5B,MAAMhC,OAAOH,YAAYG,IAAI;YAC7B,MAAM7C,QAAQ0C,YAAYyC,WAAW;YAErC,MAAMC,WAAW;gBACf/C,MAAM;gBACNO,MACE,CAAC,yBAAyB,EAAEiC,YAAY,UAAU,CAAC,GACnDrF,QAAQ,CAACqF,YAAY,CAACjF,IAAI;YAC9B;YAEA,IAAII,SAASiC,IAAAA,2BAAoB,EAACT,UAAUxB,QAAQ;oBAO9CR,+BAAAA,uBACEA;gBAPN,iCAAiC;gBACjC,MAAMqE,WAAWjC,GAAGyD,eAAe,CAACrF;gBACpC,MAAM4C,OAAOoB,IAAAA,yBAAkB,EAAChE,MAAM8C,OAAO;gBAC7C,MAAM2B,MAAMZ,WAAW,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,GAAGA;gBAErC,MAAM7C,UAAUP,QAAQ,CAACqF,YAAY,CAAC9E,OAAO,IACzCP,gCAAAA,CAAAA,wBAAAA,QAAQ,CAACqF,YAAY,EAAC9E,OAAO,qBAA7BP,mCAAAA,uBAAgCiF,OAChC,CAAC,GAACjF,gCAAAA,QAAQ,CAACqF,YAAY,CAAClF,OAAO,qBAA7BH,6BAA+B,CAACiF,IAAI;gBAE1C,IAAI1E,SAAS;wBAaHP,gCACAA,+BAAAA;oBAbR0F,aAAa;wBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;wBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;wBAChDoB,UAAU;4BACRC,OAAOxF,MAAMyF,QAAQ;4BACrBC,QAAQ1F,MAAM2F,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BACb;gCACExD,MAAM;gCACNO,MACEpD,EAAAA,iCAAAA,QAAQ,CAACqF,YAAY,CAAClF,OAAO,qBAA7BH,8BAA+B,CAACiF,IAAI,OACpCjF,gCAAAA,CAAAA,yBAAAA,QAAQ,CAACqF,YAAY,EAACpE,OAAO,qBAA7BjB,mCAAAA,wBAAgCiF,SAChC;4BACJ;4BACAW;yBACD;oBACH;gBACF,OAAO;oBACL,qCAAqC;oBACrCF,aAAa;wBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;wBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;wBAChDoB,UAAU;4BACRC,OAAOxF,MAAMyF,QAAQ;4BACrBC,QAAQ1F,MAAM2F,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BAACT;yBAAS;oBAC3B;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChCF,aAAa;oBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;oBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;oBAChDoB,UAAU;wBACRC,OAAO3C,KAAK4C,QAAQ;wBACpBC,QAAQ7C,KAAK8C,QAAQ;oBACvB;oBACAC,cAAc,EAAE;oBAChBC,eAAe;wBACb;4BACExD,MAAM;4BACNO,MAAMwB,kBAAkBS;wBAC1B;wBACAO;qBACD;gBACH;YACF;QACF;QACA,OAAOF;IACT;IAEA,iDAAiD;IACjDY,2BACEC,SAAiB,EACjBvC,IAAkC;QAElC,MAAM5B,KAAKC,IAAAA,YAAK;QAChB,IACE2B,QACAA,KAAKE,eAAe,IACpBF,KAAKE,eAAe,CAACI,UAAU,CAAC,oBAChC;YACA,IAAIkC,UAAU;YACd,IAAIxC,KAAKE,eAAe,KAAK,qCAAqC;gBAChEsC,UAAU5B,kBAAkB2B;YAC9B,OAAO;gBACL,MAAMpG,UAAUH,QAAQ,CAACgE,KAAKC,UAAU,CAAC,CAAC9D,OAAO;gBACjD,IAAI,CAACA,SAAS;gBACdqG,UAAUrG,OAAO,CAACoG,UAAU;YAC9B;YACA,OAAO;gBACLlD,MAAMkD;gBACN1D,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;gBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;gBAChDyB,cAAc,EAAE;gBAChBC,eAAe;oBACb;wBACExD,MAAM;wBACNO,MAAMoD;oBACR;iBACD;YACH;QACF;IACF;IAEA,yCAAyC;IACzCC,kDACEvE,MAA2B,EAC3BM,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,MAAMqE,cAAqC,EAAE;QAE7C,yCAAyC;QACzC,IAAItE,GAAGY,yBAAyB,CAACR,KAAKS,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAME,OAAOH,YAAYG,IAAI;gBAC7B,IAAIjB,GAAGuE,YAAY,CAACtD,OAAO;oBACzB,IAAI,CAACuD,yBAAe,CAACC,QAAQ,CAACxD,KAAKD,IAAI,KAAK,CAACpD,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAE;wBAChEsD,YAAYpB,IAAI,CAAC;4BACfwB,MAAM5E;4BACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;4BACrCC,MAAMC,wBAAc,CAACC,oBAAoB;4BACzCC,aAAa,CAAC,CAAC,EAAEhE,KAAKD,IAAI,CAAC,4CAA4C,CAAC;4BACxE4C,OAAO3C,KAAK4C,QAAQ;4BACpBC,QAAQ7C,KAAK8C,QAAQ;wBACvB;oBACF,OAAO,IAAInG,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAM5C,QAAQ0C,YAAYyC,WAAW;wBACrC,MAAMxF,UAAUH,QAAQ,CAACqD,KAAKD,IAAI,CAAC,CAACjD,OAAO;wBAE3C,IAAIK,SAASL,SAAS;4BACpB,IAAImH,iBAAiB;4BACrB,IAAIC,eAAe;4BACnB,IAAIC,YAAY;4BAEhB,IACEpF,GAAGyD,eAAe,CAACrF,UACnB4B,GAAGqF,+BAA+B,CAACjH,QACnC;oCAQGR,6BAAAA;gCAPH,MAAM0H,MAAM,MAAMlD,IAAAA,yBAAkB,EAAChE,MAAM8C,OAAO,MAAM;gCACxD,MAAMqE,gBAAgB7C,OAAOS,IAAI,CAACpF,SAASyH,MAAM,CAAC,CAAC7G,IACjD,QAAQ8G,IAAI,CAAC9G;gCAGf,IACE,CAAC4G,cAAcd,QAAQ,CAACa,QACxB,GAAC1H,8BAAAA,CAAAA,sBAAAA,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAC7C,OAAO,qBAA3BP,iCAAAA,qBAA8B0H,OAC/B;oCACAF,YAAY;oCACZF,iBAAiBI;gCACnB;4BACF,OAAO,IACLtF,GAAG0F,gBAAgB,CAACtH,UACnB4B,GAAG2F,uBAAuB,CAACvH,UAC1B4B,GAAG4F,YAAY,CAAC,AAACxH,MAAcyH,QAAQ,KACtC7F,CAAAA,GAAG0F,gBAAgB,CAAC,AAACtH,MAAc0H,OAAO,CAACrF,IAAI,KAC7CT,GAAGuE,YAAY,CAAC,AAACnG,MAAc0H,OAAO,CAACrF,IAAI,KAC1C,AAACrC,MAAc0H,OAAO,CAACrF,IAAI,CAACS,OAAO,OAAO,UAAU,KACzDlB,GAAGuE,YAAY,CAACnG,UAAUA,MAAM8C,OAAO,OAAO,YAC/C;oCAEKtD,8BAAAA;gCADL,MAAMe,IAAIP,MAAM8C,OAAO;gCACvB,IAAI,GAACtD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAC7C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCyG,YAAY;oCACZF,iBAAiBvG;gCACnB;4BACF,OAAO,IACLP,MAAMqC,IAAI,KAAKT,GAAGU,UAAU,CAACqF,WAAW,IACxC3H,MAAMqC,IAAI,KAAKT,GAAGU,UAAU,CAACsF,YAAY,EACzC;oCAEKpI,8BAAAA;gCADL,MAAMe,IAAIP,MAAM8C,OAAO;gCACvB,IAAI,GAACtD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAC7C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCyG,YAAY;oCACZF,iBAAiBvG;gCACnB;4BACF,OAAO,IAAIqB,GAAGiG,wBAAwB,CAAC7H,QAAQ;oCAG1CR,8BAAAA;gCAFH,MAAMe,IAAIP,MAAM8C,OAAO;gCACvB,IACE,GAACtD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACqD,KAAKD,IAAI,CAAC,EAAC7C,OAAO,qBAA3BP,kCAAAA,sBACCU,KAAK4H,SAAS,CAAC9H,MAAM+H,QAAQ,CAACvD,GAAG,CAAC,CAACwD,IAAMA,EAAElF,OAAO,QAEpD;oCACAkE,YAAY;oCACZF,iBAAiBvG;gCACnB;4BACF,OAAO,IACL,iBAAiB;4BACjBqB,GAAGqG,eAAe,CAACjI,UACnB4B,GAAGsG,yBAAyB,CAAClI,UAC7B4B,GAAGuG,0BAA0B,CAACnI,UAC9B4B,GAAG2F,uBAAuB,CAACvH,QAC3B;gCACAgH,YAAY;gCACZF,iBAAiB9G,MAAM8C,OAAO;4BAChC,OAAO;gCACL,8DAA8D;gCAC9DkE,YAAY;gCACZF,iBAAiB9G,MAAM8C,OAAO;gCAC9BiE,eAAe,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEjE,KAAKD,IAAI,CAAC,0DAA0D,CAAC;4BAC3I;4BAEA,IAAIoE,WAAW;gCACbd,YAAYpB,IAAI,CAAC;oCACfwB,MAAM5E;oCACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;oCACrCC,MAAMC,wBAAc,CAACyB,oBAAoB;oCACzCvB,aACEE,gBACA,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEjE,KAAKD,IAAI,CAAC,SAAS,CAAC;oCAC3E4C,OAAOxF,MAAMyF,QAAQ;oCACrBC,QAAQ1F,MAAM2F,QAAQ;gCACxB;4BACF;wBACF;oBACF,OAAO,IAAI9C,KAAKD,IAAI,KAAKyF,8BAAoB,EAAE;wBAC7C,gCAAgC;wBAChC,4BAA4B;wBAC5B,MAAMrI,QAAQ0C,YAAYyC,WAAW;wBACrC,IAAInF,SAAS4B,GAAGsG,yBAAyB,CAAClI,QAAQ;4BAChD,KAAK,MAAMsI,QAAQtI,MAAMuI,UAAU,CAAE;gCACnC,IACE3G,GAAG4G,oBAAoB,CAACF,SACxB1G,GAAGuE,YAAY,CAACmC,KAAKzF,IAAI,KACzByF,KAAKzF,IAAI,CAACD,IAAI,KAAK,OACnB;oCACAsD,YAAYpB,IAAI,CAAC;wCACfwB,MAAM5E;wCACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;wCACrCC,MAAMC,wBAAc,CAAC8B,qBAAqB;wCAC1C5B,aAAa,CAAC,0HAA0H,CAAC;wCACzIrB,OAAO8C,KAAK7C,QAAQ;wCACpBC,QAAQ4C,KAAK3C,QAAQ;oCACvB;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOO;IACT;AACF;MAEA,WAAexB"}
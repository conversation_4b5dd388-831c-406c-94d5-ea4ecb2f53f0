{"version": 3, "sources": ["../../../../src/lib/metadata/types/alternative-urls-types.ts"], "sourcesContent": ["// Reference: https://hreflang.org/what-is-a-valid-hreflang\n\ntype LangCode =\n  | 'aa'\n  | 'ab'\n  | 'ae'\n  | 'af'\n  | 'ak'\n  | 'am'\n  | 'an'\n  | 'ar'\n  | 'as'\n  | 'av'\n  | 'ay'\n  | 'az'\n  | 'ba'\n  | 'be'\n  | 'bg'\n  | 'bh'\n  | 'bi'\n  | 'bm'\n  | 'bn'\n  | 'bo'\n  | 'br'\n  | 'bs'\n  | 'ca'\n  | 'ce'\n  | 'ch'\n  | 'co'\n  | 'cr'\n  | 'cs'\n  | 'cu'\n  | 'cv'\n  | 'cy'\n  | 'da'\n  | 'de'\n  | 'dv'\n  | 'dz'\n  | 'ee'\n  | 'el'\n  | 'en'\n  | 'eo'\n  | 'es'\n  | 'et'\n  | 'eu'\n  | 'fa'\n  | 'ff'\n  | 'fi'\n  | 'fj'\n  | 'fo'\n  | 'fr'\n  | 'fy'\n  | 'ga'\n  | 'gd'\n  | 'gl'\n  | 'gn'\n  | 'gu'\n  | 'gv'\n  | 'ha'\n  | 'he'\n  | 'hi'\n  | 'ho'\n  | 'hr'\n  | 'ht'\n  | 'hu'\n  | 'hy'\n  | 'hz'\n  | 'ia'\n  | 'id'\n  | 'ie'\n  | 'ig'\n  | 'ii'\n  | 'ik'\n  | 'io'\n  | 'is'\n  | 'it'\n  | 'iu'\n  | 'ja'\n  | 'jv'\n  | 'ka'\n  | 'kg'\n  | 'ki'\n  | 'kj'\n  | 'kk'\n  | 'kl'\n  | 'km'\n  | 'kn'\n  | 'ko'\n  | 'kr'\n  | 'ks'\n  | 'ku'\n  | 'kv'\n  | 'kw'\n  | 'ky'\n  | 'la'\n  | 'lb'\n  | 'lg'\n  | 'li'\n  | 'ln'\n  | 'lo'\n  | 'lt'\n  | 'lu'\n  | 'lv'\n  | 'mg'\n  | 'mh'\n  | 'mi'\n  | 'mk'\n  | 'ml'\n  | 'mn'\n  | 'mr'\n  | 'ms'\n  | 'mt'\n  | 'my'\n  | 'na'\n  | 'nb'\n  | 'nd'\n  | 'ne'\n  | 'ng'\n  | 'nl'\n  | 'nn'\n  | 'no'\n  | 'nr'\n  | 'nv'\n  | 'ny'\n  | 'oc'\n  | 'oj'\n  | 'om'\n  | 'or'\n  | 'os'\n  | 'pa'\n  | 'pi'\n  | 'pl'\n  | 'ps'\n  | 'pt'\n  | 'qu'\n  | 'rm'\n  | 'rn'\n  | 'ro'\n  | 'ru'\n  | 'rw'\n  | 'sa'\n  | 'sc'\n  | 'sd'\n  | 'se'\n  | 'sg'\n  | 'si'\n  | 'sk'\n  | 'sl'\n  | 'sm'\n  | 'sn'\n  | 'so'\n  | 'sq'\n  | 'sr'\n  | 'ss'\n  | 'st'\n  | 'su'\n  | 'sv'\n  | 'sw'\n  | 'ta'\n  | 'te'\n  | 'tg'\n  | 'th'\n  | 'ti'\n  | 'tk'\n  | 'tl'\n  | 'tn'\n  | 'to'\n  | 'tr'\n  | 'ts'\n  | 'tt'\n  | 'tw'\n  | 'ty'\n  | 'ug'\n  | 'uk'\n  | 'ur'\n  | 'uz'\n  | 've'\n  | 'vi'\n  | 'vo'\n  | 'wa'\n  | 'wo'\n  | 'xh'\n  | 'yi'\n  | 'yo'\n  | 'za'\n  | 'zh'\n  | 'zu'\n  | 'af-ZA'\n  | 'am-ET'\n  | 'ar-AE'\n  | 'ar-BH'\n  | 'ar-DZ'\n  | 'ar-EG'\n  | 'ar-IQ'\n  | 'ar-JO'\n  | 'ar-KW'\n  | 'ar-LB'\n  | 'ar-LY'\n  | 'ar-MA'\n  | 'arn-CL'\n  | 'ar-OM'\n  | 'ar-QA'\n  | 'ar-SA'\n  | 'ar-SD'\n  | 'ar-SY'\n  | 'ar-TN'\n  | 'ar-YE'\n  | 'as-IN'\n  | 'az-az'\n  | 'az-Cyrl-AZ'\n  | 'az-Latn-AZ'\n  | 'ba-RU'\n  | 'be-BY'\n  | 'bg-BG'\n  | 'bn-BD'\n  | 'bn-IN'\n  | 'bo-CN'\n  | 'br-FR'\n  | 'bs-Cyrl-BA'\n  | 'bs-Latn-BA'\n  | 'ca-ES'\n  | 'co-FR'\n  | 'cs-CZ'\n  | 'cy-GB'\n  | 'da-DK'\n  | 'de-AT'\n  | 'de-CH'\n  | 'de-DE'\n  | 'de-LI'\n  | 'de-LU'\n  | 'dsb-DE'\n  | 'dv-MV'\n  | 'el-CY'\n  | 'el-GR'\n  | 'en-029'\n  | 'en-AU'\n  | 'en-BZ'\n  | 'en-CA'\n  | 'en-cb'\n  | 'en-GB'\n  | 'en-IE'\n  | 'en-IN'\n  | 'en-JM'\n  | 'en-MT'\n  | 'en-MY'\n  | 'en-NZ'\n  | 'en-PH'\n  | 'en-SG'\n  | 'en-TT'\n  | 'en-US'\n  | 'en-ZA'\n  | 'en-ZW'\n  | 'es-AR'\n  | 'es-BO'\n  | 'es-CL'\n  | 'es-CO'\n  | 'es-CR'\n  | 'es-DO'\n  | 'es-EC'\n  | 'es-ES'\n  | 'es-GT'\n  | 'es-HN'\n  | 'es-MX'\n  | 'es-NI'\n  | 'es-PA'\n  | 'es-PE'\n  | 'es-PR'\n  | 'es-PY'\n  | 'es-SV'\n  | 'es-US'\n  | 'es-UY'\n  | 'es-VE'\n  | 'et-EE'\n  | 'eu-ES'\n  | 'fa-IR'\n  | 'fi-FI'\n  | 'fil-PH'\n  | 'fo-FO'\n  | 'fr-BE'\n  | 'fr-CA'\n  | 'fr-CH'\n  | 'fr-FR'\n  | 'fr-LU'\n  | 'fr-MC'\n  | 'fy-NL'\n  | 'ga-IE'\n  | 'gd-GB'\n  | 'gd-ie'\n  | 'gl-ES'\n  | 'gsw-FR'\n  | 'gu-IN'\n  | 'ha-Latn-NG'\n  | 'he-IL'\n  | 'hi-IN'\n  | 'hr-BA'\n  | 'hr-HR'\n  | 'hsb-DE'\n  | 'hu-HU'\n  | 'hy-AM'\n  | 'id-ID'\n  | 'ig-NG'\n  | 'ii-CN'\n  | 'in-ID'\n  | 'is-IS'\n  | 'it-CH'\n  | 'it-IT'\n  | 'iu-Cans-CA'\n  | 'iu-Latn-CA'\n  | 'iw-IL'\n  | 'ja-JP'\n  | 'ka-GE'\n  | 'kk-KZ'\n  | 'kl-GL'\n  | 'km-KH'\n  | 'kn-IN'\n  | 'kok-IN'\n  | 'ko-KR'\n  | 'ky-KG'\n  | 'lb-LU'\n  | 'lo-LA'\n  | 'lt-LT'\n  | 'lv-LV'\n  | 'mi-NZ'\n  | 'mk-MK'\n  | 'ml-IN'\n  | 'mn-MN'\n  | 'mn-Mong-CN'\n  | 'moh-CA'\n  | 'mr-IN'\n  | 'ms-BN'\n  | 'ms-MY'\n  | 'mt-MT'\n  | 'nb-NO'\n  | 'ne-NP'\n  | 'nl-BE'\n  | 'nl-NL'\n  | 'nn-NO'\n  | 'no-no'\n  | 'nso-ZA'\n  | 'oc-FR'\n  | 'or-IN'\n  | 'pa-IN'\n  | 'pl-PL'\n  | 'prs-AF'\n  | 'ps-AF'\n  | 'pt-BR'\n  | 'pt-PT'\n  | 'qut-GT'\n  | 'quz-BO'\n  | 'quz-EC'\n  | 'quz-PE'\n  | 'rm-CH'\n  | 'ro-mo'\n  | 'ro-RO'\n  | 'ru-mo'\n  | 'ru-RU'\n  | 'rw-RW'\n  | 'sah-RU'\n  | 'sa-IN'\n  | 'se-FI'\n  | 'se-NO'\n  | 'se-SE'\n  | 'si-LK'\n  | 'sk-SK'\n  | 'sl-SI'\n  | 'sma-NO'\n  | 'sma-SE'\n  | 'smj-NO'\n  | 'smj-SE'\n  | 'smn-FI'\n  | 'sms-FI'\n  | 'sq-AL'\n  | 'sr-BA'\n  | 'sr-CS'\n  | 'sr-Cyrl-BA'\n  | 'sr-Cyrl-CS'\n  | 'sr-Cyrl-ME'\n  | 'sr-Cyrl-RS'\n  | 'sr-Latn-BA'\n  | 'sr-Latn-CS'\n  | 'sr-Latn-ME'\n  | 'sr-Latn-RS'\n  | 'sr-ME'\n  | 'sr-RS'\n  | 'sr-sp'\n  | 'sv-FI'\n  | 'sv-SE'\n  | 'sw-KE'\n  | 'syr-SY'\n  | 'ta-IN'\n  | 'te-IN'\n  | 'tg-Cyrl-TJ'\n  | 'th-TH'\n  | 'tk-TM'\n  | 'tlh-QS'\n  | 'tn-ZA'\n  | 'tr-TR'\n  | 'tt-RU'\n  | 'tzm-Latn-DZ'\n  | 'ug-CN'\n  | 'uk-UA'\n  | 'ur-PK'\n  | 'uz-Cyrl-UZ'\n  | 'uz-Latn-UZ'\n  | 'uz-uz'\n  | 'vi-VN'\n  | 'wo-SN'\n  | 'xh-ZA'\n  | 'yo-NG'\n  | 'zh-CN'\n  | 'zh-HK'\n  | 'zh-MO'\n  | 'zh-SG'\n  | 'zh-TW'\n  | 'zh-Hans'\n  | 'zh-Hant'\n  | 'zu-ZA'\n  // We can't have all valid combinations of language-region-script listed here\n  // as the list is too long and breaks the TypeScript compiler. So instead we\n  // only add the most common ones with a general string pattern for the rest.\n  // This way autocompletion still works and it is still possible to add custom\n  // lang codes.\n  | `${Lowercase<string>}-${string}`\n\ntype UnmatchedLang = 'x-default'\n\ntype HrefLang = LangCode | UnmatchedLang\n\nexport type Languages<T> = {\n  [s in HrefLang]?: T | undefined\n}\n\nexport type AlternateLinkDescriptor = {\n  title?: string | undefined\n  url: string | URL\n}\n\nexport type AlternateURLs = {\n  canonical?: null | string | URL | AlternateLinkDescriptor | undefined\n  languages?:\n    | Languages<null | string | URL | AlternateLinkDescriptor[]>\n    | undefined\n  media?:\n    | {\n        [media: string]: null | string | URL | AlternateLinkDescriptor[]\n      }\n    | undefined\n  types?:\n    | {\n        [types: string]: null | string | URL | AlternateLinkDescriptor[]\n      }\n    | undefined\n}\n\nexport type ResolvedAlternateURLs = {\n  canonical: null | AlternateLinkDescriptor\n  languages: null | Languages<AlternateLinkDescriptor[]>\n  media: null | {\n    [media: string]: null | AlternateLinkDescriptor[]\n  }\n  types: null | {\n    [types: string]: null | AlternateLinkDescriptor[]\n  }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D"}
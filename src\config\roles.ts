export interface Role {
  id: string;
  name: string;
  description: string;
  category?: string;
  isCustom?: boolean;
}

export const PREDEFINED_ROLES: Role[] = [
  {
    id: "general_chat",
    name: "<PERSON> Cha<PERSON>",
    description: "General purpose conversational AI assistant",
    category: "General"
  },
  {
    id: "code_assistant",
    name: "Code Assistant",
    description: "Specialized in programming and software development",
    category: "Development"
  },
  {
    id: "content_writer",
    name: "Content Writer",
    description: "Specialized in creating written content and copywriting",
    category: "Writing"
  },
  {
    id: "data_analyst",
    name: "Data Analyst",
    description: "Specialized in data analysis and interpretation",
    category: "Analytics"
  },
  {
    id: "research_assistant",
    name: "Research Assistant",
    description: "Specialized in research and information gathering",
    category: "Research"
  },
  {
    id: "creative_writer",
    name: "Creative Writer",
    description: "Specialized in creative writing and storytelling",
    category: "Writing"
  },
  {
    id: "technical_writer",
    name: "Technical Writer",
    description: "Specialized in technical documentation and manuals",
    category: "Writing"
  },
  {
    id: "translator",
    name: "Translator",
    description: "Specialized in language translation and localization",
    category: "Language"
  },
  {
    id: "summarizer",
    name: "Summarizer",
    description: "Specialized in summarizing and condensing information",
    category: "Analysis"
  },
  {
    id: "educator",
    name: "Educator",
    description: "Specialized in teaching and educational content",
    category: "Education"
  }
];

export const getRoleById = (roleId: string): Role | undefined => {
  return PREDEFINED_ROLES.find(role => role.id === roleId);
};

export const getRolesByCategory = (category: string): Role[] => {
  return PREDEFINED_ROLES.filter(role => role.category === category);
};

export const getAllCategories = (): string[] => {
  const categories = PREDEFINED_ROLES.map(role => role.category).filter(Boolean);
  return [...new Set(categories)] as string[];
};

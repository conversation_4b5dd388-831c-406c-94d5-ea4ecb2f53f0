{"version": 3, "sources": ["../../../../src/build/webpack/plugins/copy-file-plugin.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { sources, webpack } from 'next/dist/compiled/webpack/webpack'\n\nconst PLUGIN_NAME = 'CopyFilePlugin'\n\nexport class CopyFilePlugin {\n  private filePath: string\n  private name: string\n  private cacheKey: string\n  private info?: object\n\n  constructor({\n    filePath,\n    cacheKey,\n    name,\n    info,\n  }: {\n    filePath: string\n    cacheKey: string\n    name: string\n    minimize: boolean\n    info?: object\n  }) {\n    this.filePath = filePath\n    this.cacheKey = cacheKey\n    this.name = name\n    this.info = info\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.thisCompilation.tap(PLUGIN_NAME, (compilation: any) => {\n      const cache = compilation.getCache('CopyFilePlugin')\n      const hook = compilation.hooks.processAssets\n      hook.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        async () => {\n          if (cache) {\n            const cachedResult = await cache.getPromise(\n              this.filePath,\n              this.cacheKey\n            )\n            if (cachedResult) {\n              const { file, source } = cachedResult\n              compilation.emitAsset(file, source, {\n                ...this.info,\n              })\n              return\n            }\n          }\n          const content = await fs.readFile(this.filePath, 'utf8')\n\n          const file = loaderUtils.interpolateName(\n            { resourcePath: this.filePath },\n            this.name,\n            { content, context: compiler.context }\n          )\n\n          const source = new sources.RawSource(content)\n\n          if (cache) {\n            await cache.storePromise(this.filePath, this.cacheKey, {\n              file,\n              source,\n            })\n          }\n\n          compilation.emitAsset(file, source, {\n            ...this.info,\n          })\n        }\n      )\n    })\n  }\n}\n"], "names": ["promises", "fs", "loaderUtils", "sources", "webpack", "PLUGIN_NAME", "CopyFilePlugin", "constructor", "filePath", "cache<PERSON>ey", "name", "info", "apply", "compiler", "hooks", "thisCompilation", "tap", "compilation", "cache", "getCache", "hook", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "cachedResult", "getPromise", "file", "source", "emitAsset", "content", "readFile", "interpolateName", "resourcePath", "context", "RawSource", "storePromise"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,iBAAiB,mCAAkC;AAC1D,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AAErE,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAMXC,YAAY,EACVC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,IAAI,EAOL,CAAE;QACD,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACC,IAAI,GAAGA;QACZ,IAAI,CAACC,IAAI,GAAGA;IACd;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,eAAe,CAACC,GAAG,CAACX,aAAa,CAACY;YAC/C,MAAMC,QAAQD,YAAYE,QAAQ,CAAC;YACnC,MAAMC,OAAOH,YAAYH,KAAK,CAACO,aAAa;YAC5CD,KAAKE,UAAU,CACb;gBACEZ,MAAML;gBACNkB,OAAOnB,QAAQoB,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,IAAIP,OAAO;oBACT,MAAMQ,eAAe,MAAMR,MAAMS,UAAU,CACzC,IAAI,CAACnB,QAAQ,EACb,IAAI,CAACC,QAAQ;oBAEf,IAAIiB,cAAc;wBAChB,MAAM,EAAEE,IAAI,EAAEC,MAAM,EAAE,GAAGH;wBACzBT,YAAYa,SAAS,CAACF,MAAMC,QAAQ;4BAClC,GAAG,IAAI,CAAClB,IAAI;wBACd;wBACA;oBACF;gBACF;gBACA,MAAMoB,UAAU,MAAM9B,GAAG+B,QAAQ,CAAC,IAAI,CAACxB,QAAQ,EAAE;gBAEjD,MAAMoB,OAAO1B,YAAY+B,eAAe,CACtC;oBAAEC,cAAc,IAAI,CAAC1B,QAAQ;gBAAC,GAC9B,IAAI,CAACE,IAAI,EACT;oBAAEqB;oBAASI,SAAStB,SAASsB,OAAO;gBAAC;gBAGvC,MAAMN,SAAS,IAAI1B,QAAQiC,SAAS,CAACL;gBAErC,IAAIb,OAAO;oBACT,MAAMA,MAAMmB,YAAY,CAAC,IAAI,CAAC7B,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE;wBACrDmB;wBACAC;oBACF;gBACF;gBAEAZ,YAAYa,SAAS,CAACF,MAAMC,QAAQ;oBAClC,GAAG,IAAI,CAAClB,IAAI;gBACd;YACF;QAEJ;IACF;AACF"}
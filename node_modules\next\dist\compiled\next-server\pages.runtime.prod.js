(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,o={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function a(e){let t=/* @__PURE__ */new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,s],...o]=a(e),{domain:i,expires:l,httponly:c,maxage:p,path:h,samesite:m,secure:f,partitioned:g,priority:v}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(s),domain:i,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...m&&{sameSite:d.includes(t=(t=m).toLowerCase())?t:void 0},...f&&{secure:!0},...v&&{priority:u.includes(r=(r=v).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>i}),e.exports=((e,o,i,a)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let i of n(o))s.call(e,i)||void 0===i||t(e,i,{get:()=>o[i],enumerable:!(a=r(o,i))||a.enumerable});return e})(t({},"__esModule",{value:!0}),o);var d=["strict","lax","none"],u=["low","medium","high"],c=class{constructor(e){this._parsed=/* @__PURE__ */new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=/* @__PURE__ */new Map,this._headers=e;let s=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,n,s,o,i=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,o=!1;l();)if(","===(r=e.charAt(a))){for(n=a,a+=1,l(),s=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(o=!0,a=s,i.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!o||a>=e.length)&&i.push(e.substring(t,e.length))}return i}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:/* @__PURE__ */new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},o=t.split(n),i=(r||{}).decode||e,a=0;a<o.length;a++){var l=o[a],d=l.indexOf("=");if(!(d<0)){var u=l.substr(0,d).trim(),c=l.substr(++d,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==s[u]&&(s[u]=function(e,t){try{return t(e)}catch(t){return e}}(c,i))}}return s},t.serialize=function(e,t,n){var o=n||{},i=o.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var a=i(t);if(a&&!s.test(a))throw TypeError("argument val is invalid");var l=e+"="+a;if(null!=o.maxAge){var d=o.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(o.domain){if(!s.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!s.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},"./dist/compiled/react-is/cjs/react-is.production.js":(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler");Symbol.for("react.provider");var a=Symbol.for("react.consumer"),l=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),c=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen"),f=Symbol.for("react.client.reference");function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case s:case i:case o:case u:case c:return e;default:switch(e=e&&e.$$typeof){case l:case d:case h:case p:case a:return e;default:return t}}case n:return t}}}t.ContextConsumer=a,t.ContextProvider=l,t.Element=r,t.ForwardRef=d,t.Fragment=s,t.Lazy=h,t.Memo=p,t.Portal=n,t.Profiler=i,t.StrictMode=o,t.Suspense=u,t.SuspenseList=c,t.isContextConsumer=function(e){return g(e)===a},t.isContextProvider=function(e){return g(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return g(e)===d},t.isFragment=function(e){return g(e)===s},t.isLazy=function(e){return g(e)===h},t.isMemo=function(e){return g(e)===p},t.isPortal=function(e){return g(e)===n},t.isProfiler=function(e){return g(e)===i},t.isStrictMode=function(e){return g(e)===o},t.isSuspense=function(e){return g(e)===u},t.isSuspenseList=function(e){return g(e)===c},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===i||e===o||e===u||e===c||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===p||e.$$typeof===l||e.$$typeof===a||e.$$typeof===d||e.$$typeof===f||void 0!==e.getModuleId)},t.typeOf=g},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.production.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,t,r)=>{let n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var s=r[e];if(void 0!==s)return s.exports;var o=r[e]={exports:{}},i=!0;try{t[e](o,o.exports,n),i=!1}finally{i&&delete r[e]}return o.exports}n.ab=__dirname+"/";var s=n(532);e.exports=s})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";var n;r.d(t,{R8:()=>v});let{env:s,stdout:o}=(null==(n=globalThis)?void 0:n.process)??{},i=s&&!s.NO_COLOR&&(s.FORCE_COLOR||(null==o?void 0:o.isTTY)&&!s.CI&&"dumb"!==s.TERM),a=(e,t,r,n)=>{let s=e.substring(0,n)+r,o=e.substring(n+t.length),i=o.indexOf(t);return~i?s+a(o,t,r,i):s+o},l=(e,t,r=e)=>i?n=>{let s=""+n,o=s.indexOf(t,e.length);return~o?e+a(s,t,r,o)+t:e+s+t}:String,d=l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),l("\x1b[3m","\x1b[23m"),l("\x1b[4m","\x1b[24m"),l("\x1b[7m","\x1b[27m"),l("\x1b[8m","\x1b[28m"),l("\x1b[9m","\x1b[29m"),l("\x1b[30m","\x1b[39m");let u=l("\x1b[31m","\x1b[39m"),c=l("\x1b[32m","\x1b[39m"),p=l("\x1b[33m","\x1b[39m");l("\x1b[34m","\x1b[39m");let h=l("\x1b[35m","\x1b[39m");l("\x1b[38;2;173;127;168m","\x1b[39m"),l("\x1b[36m","\x1b[39m");let m=l("\x1b[37m","\x1b[39m");l("\x1b[90m","\x1b[39m"),l("\x1b[40m","\x1b[49m"),l("\x1b[41m","\x1b[49m"),l("\x1b[42m","\x1b[49m"),l("\x1b[43m","\x1b[49m"),l("\x1b[44m","\x1b[49m"),l("\x1b[45m","\x1b[49m"),l("\x1b[46m","\x1b[49m"),l("\x1b[47m","\x1b[49m");let f={wait:m(d("○")),error:u(d("⨯")),warn:p(d("⚠")),ready:"▲",info:m(d(" ")),event:c(d("✓")),trace:h(d("»"))},g={log:"log",warn:"warn",error:"error"};function v(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in g?g[e]:"log",n=f[e];0===t.length?console[r](""):1===t.length&&"string"==typeof t[0]?console[r](" "+n+" "+t[0]):console[r](" "+n,...t)}("warn",...e)}new class{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize){console.warn("Single item size exceeds maxSize");return}this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}(1e4,e=>e.length)},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{$1:()=>p,Oh:()=>h,UO:()=>c,_I:()=>d,bX:()=>a,g0:()=>l,iS:()=>i,kz:()=>n,qF:()=>o,r4:()=>s,xV:()=>u});let n="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",o=31536e3,i="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",a="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",l="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",d="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",u="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",c="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",p="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",h="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",m={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser"};({...m,GROUP:{builtinReact:[m.reactServerComponents,m.actionBrowser],serverOnly:[m.reactServerComponents,m.actionBrowser,m.instrument,m.middleware],neutralTarget:[m.api],clientOnly:[m.serverSideRendering,m.appPagesBrowser],bundled:[m.reactServerComponents,m.actionBrowser,m.serverSideRendering,m.appPagesBrowser,m.shared,m.instrument],appPages:[m.reactServerComponents,m.serverSideRendering,m.appPagesBrowser,m.actionBrowser]}})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{C4:()=>c,Gx:()=>o,Ic:()=>i,PW:()=>l,Uc:()=>a,wE:()=>u});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),s=r("./dist/esm/lib/constants.js");function o(e,t){let r=n.o.from(e.headers);return{isOnDemandRevalidate:r.get(s.kz)===t.previewModeId,revalidateOnlyGenerated:r.has(s.r4)}}r("./lib/trace/tracer"),r("./dist/esm/server/lib/trace/constants.js");let i="__prerender_bypass",a="__next_preview_data",l=Symbol(a),d=Symbol(i);function u(e,t={}){if(d in e)return e;let{serialize:n}=r("./dist/compiled/cookie/index.js"),s=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof s?[s]:Array.isArray(s)?s:[],n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(a,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,d,{value:!0,enumerable:!1}),e}function c({req:e},t,r){let n={configurable:!0,enumerable:!0},s={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{let n=r();return Object.defineProperty(e,t,{...s,value:n}),n},set:r=>{Object.defineProperty(e,t,{...s,value:r})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var n=r("./dist/esm/server/api-utils/index.js"),s=r("./dist/esm/server/web/spec-extension/cookies.js"),o=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function i(e,t,i,a){var l,d;let u;if(i&&(0,n.Gx)(e,i).isOnDemandRevalidate)return!1;if(n.PW in e)return e[n.PW];let c=o.o.from(e.headers),p=new s.tm(c),h=null==(l=p.get(n.Ic))?void 0:l.value,m=null==(d=p.get(n.Uc))?void 0:d.value;if(h&&!m&&h===i.previewModeId){let t={};return Object.defineProperty(e,n.PW,{value:t,enumerable:!1}),t}if(!h&&!m)return!1;if(!h||!m||h!==i.previewModeId)return a||(0,n.wE)(t),!1;try{u=r("next/dist/compiled/jsonwebtoken").verify(m,i.previewModeSigningKey)}catch{return(0,n.wE)(t),!1}let{decryptWithSecret:f}=r("./dist/esm/server/crypto-utils.js"),g=f(Buffer.from(i.previewModeEncryptionKey),u.data);try{let t=JSON.parse(g);return Object.defineProperty(e,n.PW,{value:t,enumerable:!1}),t}catch{return!1}}},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>a,encryptWithSecret:()=>i});let n=require("crypto");var s=/*#__PURE__*/r.n(n);let o="aes-256-gcm";function i(e,t){let r=s().randomBytes(16),n=s().randomBytes(64),i=s().pbkdf2Sync(e,n,1e5,32,"sha512"),a=s().createCipheriv(o,i,r),l=Buffer.concat([a.update(t,"utf8"),a.final()]),d=a.getAuthTag();return Buffer.concat([n,r,d,l]).toString("hex")}function a(e,t){let r=Buffer.from(t,"hex"),n=r.slice(0,64),i=r.slice(64,80),a=r.slice(80,96),l=r.slice(96),d=s().pbkdf2Sync(e,n,1e5,32,"sha512"),u=s().createDecipheriv(o,d,i);return u.setAuthTag(a),u.update(l)+u.final("utf8")}},"./dist/esm/server/lib/trace/constants.js":(e,t,r)=>{"use strict";r.d(t,{Fx:()=>i,Wc:()=>d,vr:()=>l});var n=/*#__PURE__*/function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(n||{}),s=/*#__PURE__*/function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(s||{}),o=/*#__PURE__*/function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(o||{}),i=/*#__PURE__*/function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(i||{}),a=/*#__PURE__*/function(e){return e.startServer="startServer.startServer",e}(a||{}),l=/*#__PURE__*/function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(l||{}),d=/*#__PURE__*/function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(d||{}),u=/*#__PURE__*/function(e){return e.executeRoute="Router.executeRoute",e}(u||{}),c=/*#__PURE__*/function(e){return e.runHandler="Node.runHandler",e}(c||{}),p=/*#__PURE__*/function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(p||{}),h=/*#__PURE__*/function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(h||{}),m=/*#__PURE__*/function(e){return e.execute="Middleware.execute",e}(m||{})},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}return n.create(t).transformHtml(e,t)}r.d(t,{A:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";function n(e){return null!=e}async function s(e,t,s,{inAmpMode:o,hybridAmp:i}){for(let a of[o?async t=>{let n=r("./dist/esm/server/optimize-amp.js").A;return t=await n(t,s.ampOptimizerConfig),!s.ampSkipValidation&&s.ampValidator&&await s.ampValidator(t,e),t}:null,(0,s.optimizeCss)?async e=>{let t=new(r("critters"))({ssrMode:!0,reduceInlineStyles:!1,path:s.distDir,publicPath:`${s.assetPrefix}/_next/`,preload:"media",fonts:!1,logLevel:process.env.CRITTERS_LOG_LEVEL||"warn",...s.optimizeCss});return await t.process(e)}:null,o||i?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(n))a&&(t=await a(t));return t}r.d(t,{F:()=>s})},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{o:()=>o});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return n.l.get(t,r,s);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==i)return n.l.get(t,i,s)},set(t,r,s,o){if("symbol"==typeof r)return n.l.set(t,r,s,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return n.l.set(t,a??r,s,o)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==o&&n.l.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===o||n.l.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{tm:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},"./dist/server/ReactDOMServerPages.js":(e,t,r)=>{"use strict";let n;try{n=r("react-dom/server.edge")}catch(e){if("MODULE_NOT_FOUND"!==e.code&&"ERR_PACKAGE_PATH_NOT_EXPORTED"!==e.code)throw e;n=r("react-dom/server.browser")}e.exports=n},"./lib/trace/tracer":e=>{"use strict";e.exports=require("next/dist/server/lib/trace/tracer")},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"react-dom/server.browser":e=>{"use strict";e.exports=require("react-dom/server.browser")},"react-dom/server.edge":e=>{"use strict";e.exports=require("react-dom/server.edge")},path:e=>{"use strict";e.exports=require("path")}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";let e,t,s;r.r(n),r.d(n,{PagesRouteModule:()=>e9,default:()=>e5,renderToHTML:()=>e2,vendored:()=>e6});var o={};r.r(o),r.d(o,{AmpStateContext:()=>O});var i={};r.r(i),r.d(i,{HeadManagerContext:()=>L});var a={};r.r(a),r.d(a,{LoadableContext:()=>A});var l={};r.r(l),r.d(l,{default:()=>B});var d={};r.r(d),r.d(d,{RouterContext:()=>H});var u={};r.r(u),r.d(u,{HtmlContext:()=>Z,useHtmlContext:()=>Y});var c={};r.r(c),r.d(c,{ImageConfigContext:()=>eN});var p={};r.r(p),r.d(p,{PathParamsContext:()=>ek,PathnameContext:()=>eI,SearchParamsContext:()=>eA});var h={};r.r(h),r.d(h,{AppRouterContext:()=>eq,GlobalLayoutRouterContext:()=>eW,LayoutRouterContext:()=>eU,MissingSlotContext:()=>eV,TemplateContext:()=>eG});var m={};r.r(m),r.d(m,{ServerInsertedHTMLContext:()=>e3,useServerInsertedHTML:()=>e8});var f={};r.r(f),r.d(f,{AmpContext:()=>o,AppRouterContext:()=>h,HeadManagerContext:()=>i,HooksClientContext:()=>p,HtmlContext:()=>u,ImageConfigContext:()=>c,Loadable:()=>l,LoadableContext:()=>a,RouterContext:()=>d,ServerInsertedHtml:()=>m});class g{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}let v=require("react/jsx-runtime");var y=r("./dist/esm/server/api-utils/index.js");let x=require("react");var b=/*#__PURE__*/r.n(x),S=r("./dist/server/ReactDOMServerPages.js"),w=/*#__PURE__*/r.n(S);let P=require("styled-jsx");var _=r("./dist/esm/lib/constants.js");r("./dist/esm/shared/lib/modern-browserslist-target.js");let C={client:"client",server:"server",edgeServer:"edge-server"};C.client,C.server,C.edgeServer,Symbol("polyfills");let R=["/500"];function j(e){return Object.prototype.toString.call(e)}function E(e){if("[object Object]"!==j(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}let T=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class N extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".
Reason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".
Reason: ${n}`)}}function $(e,t,r){if(!E(r))throw new N(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${j(r)}\`).`);function n(r,n,s){if(r.has(n))throw new N(e,t,s,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,s)}return function r(s,o,i){let a=typeof o;if(null===o||"boolean"===a||"number"===a||"string"===a)return!0;if("undefined"===a)throw new N(e,t,i,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(E(o)){if(n(s,o,i),Object.entries(o).every(([e,t])=>{let n=T.test(e)?`${i}.${e}`:`${i}[${JSON.stringify(e)}]`,o=new Map(s);return r(o,e,n)&&r(o,t,n)}))return!0;throw new N(e,t,i,"invariant: Unknown error encountered in Object.")}if(Array.isArray(o)){if(n(s,o,i),o.every((e,t)=>r(new Map(s),e,`${i}[${t}]`)))return!0;throw new N(e,t,i,"invariant: Unknown error encountered in Array.")}throw new N(e,t,i,"`"+a+"`"+("object"===a?` ("${Object.prototype.toString.call(o)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}let O=b().createContext({}),L=b().createContext({}),A=b().createContext(null),I=[],k=[];function M(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class z{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function D(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function s(){if(!n){let t=new z(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function o(e,t){!function(){s();let e=b().useContext(A);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let o=b().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return b().useImperativeHandle(t,()=>({retry:n.retry}),[]),b().useMemo(()=>{var t;return o.loading||o.error?/*#__PURE__*/b().createElement(r.loading,{isLoading:o.loading,pastDelay:o.pastDelay,timedOut:o.timedOut,error:o.error,retry:n.retry}):o.loaded?/*#__PURE__*/b().createElement((t=o.loaded)&&t.default?t.default:t,e):null},[e,o])}return I.push(s),o.preload=()=>s(),o.displayName="LoadableComponent",/*#__PURE__*/b().forwardRef(o)}(M,e)}function F(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return F(e,t)})}D.preloadAll=()=>new Promise((e,t)=>{F(I).then(e,t)}),D.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>t();F(k,e).then(r,r)}));let B=D,H=b().createContext(null);function q(e){return e.startsWith("/")?e:"/"+e}let U=["(..)(..)","(.)","(..)","(...)"],W=/\/\[[^/]+?\](?=\/|$)/;function G(e){return void 0!==e.split("/").find(e=>U.find(t=>e.startsWith(t)))&&(e=function(e){let t,r,n;for(let s of e.split("/"))if(r=U.find(e=>s.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=q(t.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")),r){case"(.)":n="/"===t?`/${n}`:t+"/"+n;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);n=s.slice(0,-2).concat(n).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:n}}(e).interceptedRoute),W.test(e)}function V(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function J(e){return e.finished||e.headersSent}async function X(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await X(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&J(r))return n;if(!n)throw Error('"'+V(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class Q extends Error{}let Z=(0,x.createContext)(void 0);function Y(){let e=(0,x.useContext)(Z);if(!e)throw Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}let K=Symbol.for("NextInternalRequestMeta");function ee(e,t){let r=e[K]||{};return"string"==typeof t?r[t]:r}var et=/*#__PURE__*/function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});let er=new Set([301,302,303,307,308]);function en(e){return e.statusCode||(e.permanent?et.PermanentRedirect:et.TemporaryRedirect)}var es=r("./lib/trace/tracer"),eo=r("./dist/esm/server/lib/trace/constants.js");function ei(){}new Uint8Array([60,104,116,109,108]),new Uint8Array([60,98,111,100,121]),new Uint8Array([60,47,104,101,97,100,62]),new Uint8Array([60,47,98,111,100,121,62]),new Uint8Array([60,47,104,116,109,108,62]),new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62]);let ea=new TextEncoder;function el(e){return new ReadableStream({start(t){t.enqueue(e),t.close()}})}async function ed(e){let t=e.getReader(),r=[];for(;;){let{done:e,value:n}=await t.read();if(e)break;r.push(n)}return Buffer.concat(r)}async function eu(e){let t=new TextDecoder("utf-8",{fatal:!0}),r="";for await(let n of e)r+=t.decode(n,{stream:!0});return r+t.decode()}function ec(e){return e.replace(/\/$/,"")||"/"}function ep(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function eh(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=ep(e);return""+t+r+n+s}function em(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=ep(e);return""+r+t+n+s}function ef(e,t){if("string"!=typeof e)return!1;let{pathname:r}=ep(e);return r===t||r.startsWith(t+"/")}function eg(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let ev=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function ey(e,t){return new URL(String(e).replace(ev,"localhost"),t&&String(t).replace(ev,"localhost"))}let ex=Symbol("NextURLInternal");class eb{constructor(e,t,r){let n,s;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,s=r||{}):s=r||t||{},this[ex]={url:ey(e,n??s.base),options:s,basePath:""},this.analyze()}analyze(){var e,t,r,n,s;let o=function(e,t){var r,n;let{basePath:s,i18n:o,trailingSlash:i}=null!=(r=t.nextConfig)?r:{},a={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):i};s&&ef(a.pathname,s)&&(a.pathname=function(e,t){if(!ef(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(a.pathname,s),a.basePath=s);let l=a.pathname;if(a.pathname.startsWith("/_next/data/")&&a.pathname.endsWith(".json")){let e=a.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];a.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(a.pathname=l)}if(o){let e=t.i18nProvider?t.i18nProvider.analyze(a.pathname):eg(a.pathname,o.locales);a.locale=e.detectedLocale,a.pathname=null!=(n=e.pathname)?n:a.pathname,!e.detectedLocale&&a.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):eg(l,o.locales)).detectedLocale&&(a.locale=e.detectedLocale)}return a}(this[ex].url.pathname,{nextConfig:this[ex].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[ex].options.i18nProvider}),i=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[ex].url,this[ex].options.headers);this[ex].domainLocale=this[ex].options.i18nProvider?this[ex].options.i18nProvider.detectDomainLocale(i):function(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,s;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(s=o.locales)?void 0:s.some(e=>e.toLowerCase()===r)))return o}}(null==(t=this[ex].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,i);let a=(null==(r=this[ex].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[ex].options.nextConfig)?void 0:null==(n=s.i18n)?void 0:n.defaultLocale);this[ex].url.pathname=o.pathname,this[ex].defaultLocale=a,this[ex].basePath=o.basePath??"",this[ex].buildId=o.buildId,this[ex].locale=o.locale??a,this[ex].trailingSlash=o.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let s=e.toLowerCase();return!n&&(ef(s,"/api")||ef(s,"/"+t.toLowerCase()))?e:eh(e,"/"+t)}((e={basePath:this[ex].basePath,buildId:this[ex].buildId,defaultLocale:this[ex].options.forceLocale?void 0:this[ex].defaultLocale,locale:this[ex].locale,pathname:this[ex].url.pathname,trailingSlash:this[ex].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=ec(t)),e.buildId&&(t=em(eh(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=eh(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:em(t,"/"):ec(t)}formatSearch(){return this[ex].url.search}get buildId(){return this[ex].buildId}set buildId(e){this[ex].buildId=e}get locale(){return this[ex].locale??""}set locale(e){var t,r;if(!this[ex].locale||!(null==(r=this[ex].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[ex].locale=e}get defaultLocale(){return this[ex].defaultLocale}get domainLocale(){return this[ex].domainLocale}get searchParams(){return this[ex].url.searchParams}get host(){return this[ex].url.host}set host(e){this[ex].url.host=e}get hostname(){return this[ex].url.hostname}set hostname(e){this[ex].url.hostname=e}get port(){return this[ex].url.port}set port(e){this[ex].url.port=e}get protocol(){return this[ex].url.protocol}set protocol(e){this[ex].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[ex].url=ey(e),this.analyze()}get origin(){return this[ex].url.origin}get pathname(){return this[ex].url.pathname}set pathname(e){this[ex].url.pathname=e}get hash(){return this[ex].url.hash}set hash(e){this[ex].url.hash=e}get search(){return this[ex].url.search}set search(e){this[ex].url.search=e}get password(){return this[ex].url.password}set password(e){this[ex].url.password=e}get username(){return this[ex].url.username}set username(e){this[ex].url.username=e}get basePath(){return this[ex].basePath}set basePath(e){this[ex].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new eb(String(this),this[ex].options)}}r("./dist/esm/server/web/spec-extension/cookies.js"),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let eS="ResponseAborted";class ew extends Error{constructor(...e){super(...e),this.name=eS}}class eP{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}let e_=0,eC=0,eR=0;function ej(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===eS}async function eE(e,t,r){try{let{errored:n,destroyed:s}=t;if(n||s)return;let o=function(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new ew)}),t}(t),i=function(e,t){let r=!1,n=new eP;function s(){n.resolve()}e.on("drain",s),e.once("close",()=>{e.off("drain",s),n.resolve()});let o=new eP;return e.once("finish",()=>{o.resolve()}),new WritableStream({write:async t=>{if(!r){if(r=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let e=function(e={}){let t=0===e_?void 0:{clientComponentLoadStart:e_,clientComponentLoadTimes:eC,clientComponentLoadCount:eR};return e.reset&&(e_=0,eC=0,eR=0),t}();e&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:e.clientComponentLoadStart,end:e.clientComponentLoadStart+e.clientComponentLoadTimes})}e.flushHeaders(),(0,es.getTracer)().trace(eo.Fx.startResponse,{spanName:"start response"},()=>void 0)}try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new eP)}catch(t){throw e.end(),Error("failed to write chunk to response",{cause:t})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),o.promise}})}(t,r);await e.pipeTo(i,{signal:o.signal})}catch(e){if(ej(e))return;throw Error("failed to pipe response",{cause:e})}}class eT{static fromStatic(e){return new eT(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedBuffer(e=!1){if(null===this.response)throw Error("Invariant: null responses cannot be unchunked");if("string"!=typeof this.response){if(!e)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return ed(this.readable)}return Buffer.from(this.response)}toUnchunkedString(e=!1){if(null===this.response)throw Error("Invariant: null responses cannot be unchunked");if("string"!=typeof this.response){if(!e)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return eu(this.readable)}return this.response}get readable(){if(null===this.response)throw Error("Invariant: null responses cannot be streamed");if("string"==typeof this.response)throw Error("Invariant: static responses cannot be streamed");return Buffer.isBuffer(this.response)?el(this.response):Array.isArray(this.response)?function(...e){if(0===e.length)throw Error("Invariant: chainStreams requires at least one stream");if(1===e.length)return e[0];let{readable:t,writable:r}=new TransformStream,n=e[0].pipeTo(r,{preventClose:!0}),s=1;for(;s<e.length-1;s++){let t=e[s];n=n.then(()=>t.pipeTo(r,{preventClose:!0}))}let o=e[s];return(n=n.then(()=>o.pipeTo(r))).catch(ei),t}(...this.response):this.response}chain(e){var t;let r;if(null===this.response)throw Error("Invariant: response is null. This is a bug in Next.js");"string"==typeof this.response?r=[(t=this.response,new ReadableStream({start(e){e.enqueue(ea.encode(t)),e.close()}}))]:Array.isArray(this.response)?r=this.response:Buffer.isBuffer(this.response)?r=[el(this.response)]:r=[this.response],r.push(e),this.response=r}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if(ej(t)){await e.abort(t);return}throw t}}async pipeToNodeResponse(e){await eE(this.readable,e,this.waitUntil)}}let eN=b().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],unoptimized:!1});var e$=r("./dist/compiled/strip-ansi/index.js"),eO=/*#__PURE__*/r.n(e$);let eL=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eA=(0,x.createContext)(null),eI=(0,x.createContext)(null),ek=(0,x.createContext)(null),eM=/[|\\{}()[\]^$+*?.-]/,ez=/[|\\{}()[\]^$+*?.-]/g;function eD(e){return eM.test(e)?e.replace(ez,"\\$&"):e}let eF=/\[((?:\[.*\])|.+)\]/;function eB(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function eH(e){let{children:t,router:r,...n}=e,s=(0,x.useRef)(n.isAutoExport),o=(0,x.useMemo)(()=>{let e;let t=s.current;if(t&&(s.current=!1),G(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return/*#__PURE__*/(0,v.jsx)(eI.Provider,{value:o,children:t})}let eq=b().createContext(null),eU=b().createContext(null),eW=b().createContext(null),eG=b().createContext(null),eV=b().createContext(new Set),eJ=Symbol.for("NextjsError"),eX="<!DOCTYPE html>";function eQ(){throw Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function eZ(e){let t=await w().renderToReadableStream(e);return await t.allReady,eu(t)}e=r("./dist/esm/server/api-utils/node/try-get-preview-data.js").m,t=r("./dist/esm/build/output/log.js").R8,s=r("./dist/esm/server/post-process.js").F;class eY{constructor(e,t,r,{isFallback:n},s,o,i,a,l,d,u,c){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=o,this.locale=i,this.locales=a,this.defaultLocale=l,this.isReady=s,this.domainLocales=d,this.isPreview=!!u,this.isLocaleDomain=!!c}push(){eQ()}replace(){eQ()}reload(){eQ()}back(){eQ()}forward(){eQ()}prefetch(){eQ()}beforePopState(){eQ()}}function eK(e,t,r){return/*#__PURE__*/(0,v.jsx)(e,{Component:t,...r})}let e0=(e,t)=>{let r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${t.join(", ")}.
Read more: https://nextjs.org/docs/messages/${r}`};function e1(e,t,r){let{destination:n,permanent:s,statusCode:o,basePath:i}=e,a=[],l=void 0!==o,d=void 0!==s;d&&l?a.push("`permanent` and `statusCode` can not both be provided"):d&&"boolean"!=typeof s?a.push("`permanent` must be `true` or `false`"):l&&!er.has(o)&&a.push(`\`statusCode\` must undefined or one of ${[...er].join(", ")}`);let u=typeof n;"string"!==u&&a.push(`\`destination\` should be string but received ${u}`);let c=typeof i;if("undefined"!==c&&"boolean"!==c&&a.push(`\`basePath\` should be undefined or a false, received ${c}`),a.length>0)throw Error(`Invalid redirect object returned from ${r} for ${t.url}
`+a.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}async function e4(n,o,i,a,l,d){var u,c;let p,h,m,f;(0,y.C4)({req:n},"cookies",(u=n.headers,function(){let{cookie:e}=u;if(!e)return{};let{parse:t}=r("./dist/compiled/cookie/index.js");return t(Array.isArray(e)?e.join("; "):e)}));let g={};if(g.assetQueryString=l.dev&&l.assetQueryString||"",l.dev&&!g.assetQueryString){let e=(n.headers["user-agent"]||"").toLowerCase();e.includes("safari")&&!e.includes("chrome")&&(g.assetQueryString=`?ts=${Date.now()}`)}l.deploymentId&&(g.assetQueryString+=`${g.assetQueryString?"&":"?"}dpl=${l.deploymentId}`),a=Object.assign({},a);let{err:x,dev:b=!1,ampPath:S="",pageConfig:C={},buildManifest:j,reactLoadableManifest:E,ErrorDebug:T,getStaticProps:N,getStaticPaths:I,getServerSideProps:k,isNextDataRequest:M,params:z,previewProps:D,basePath:F,images:W,runtime:Y,isExperimentalCompile:K,expireTime:et}=l,{App:er}=d,ei=g.assetQueryString,ea=d.Document,el=l.Component,ed=!!a.__nextFallback,ep=a.__nextNotFoundSrcPage;!function(e){for(let t of eL)delete e[t]}(a);let eh=!!N,em=eh&&l.nextExport,ef=er.getInitialProps===er.origGetInitialProps,eg=!!(null==el?void 0:el.getInitialProps),ev=null==el?void 0:el.unstable_scriptLoader,ey=G(i),ex="/_error"===i&&el.getInitialProps===el.origGetInitialProps;l.nextExport&&eg&&!ex&&t(`Detected getInitialProps on page '${i}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let eb=!eg&&ef&&!eh&&!k;if(eb&&!b&&K&&(o.setHeader("Cache-Control",function({revalidate:e,expireTime:t}){let r="number"==typeof e&&void 0!==t?e>=t?"":`stale-while-revalidate=${t-e}`:"stale-while-revalidate";return 0===e?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof e?`s-maxage=${e}, ${r}`:`s-maxage=${_.qF}, ${r}`}({revalidate:!1,expireTime:et})),eb=!1),eg&&eh)throw Error(_.iS+` ${i}`);if(eg&&k)throw Error(_.bX+` ${i}`);if(k&&eh)throw Error(_.g0+` ${i}`);if(k&&"export"===l.nextConfigOutput)throw Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(I&&!ey)throw Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${i}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(I&&!eh)throw Error(`getStaticPaths was added without a getStaticProps in ${i}. Without getStaticProps, getStaticPaths does nothing`);if(eh&&ey&&!I)throw Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${i}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let eS=l.resolvedAsPath||n.url;if(b){let{isValidElementType:e}=r("./dist/compiled/react-is/index.js");if(!e(el))throw Error(`The default export is not a React Component in page: "${i}"`);if(!e(er))throw Error('The default export is not a React Component in page: "/_app"');if(!e(ea))throw Error('The default export is not a React Component in page: "/_document"');if((eb||ed)&&(a={...a.amp?{amp:a.amp}:{}},eS=`${i}${n.url.endsWith("/")&&"/"!==i&&!ey?"/":""}`,n.url=i),"/404"===i&&(eg||k))throw Error(`\`pages/404\` ${_._I}`);if(R.includes(i)&&(eg||k))throw Error(`\`pages${i}\` ${_._I}`)}for(let e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==el?void 0:el[e])throw Error(`page ${i} ${e} ${_.Oh}`);await B.preloadAll(),(eh||k)&&!ed&&D&&(m=!1!==(p=e(n,o,D,!!l.multiZoneDraftMode)));let ew=new eY(i,a,eS,{isFallback:ed},!!(k||eg||!ef&&!eh||K),F,l.locale,l.locales,l.defaultLocale,l.domainLocales,m,ee(n,"isLocaleDomain")),eP={back(){ew.back()},forward(){ew.forward()},refresh(){ew.reload()},hmrRefresh(){},push(e,t){let{scroll:r}=void 0===t?{}:t;ew.push(e,void 0,{scroll:r})},replace(e,t){let{scroll:r}=void 0===t?{}:t;ew.replace(e,void 0,{scroll:r})},prefetch(e){ew.prefetch(e)}},e_={},eC=(0,P.createStyleRegistry)(),eR={ampFirst:!0===C.amp,hasQuery:!!a.amp,hybrid:"hybrid"===C.amp},ej=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(eR),eE=function(e){void 0===e&&(e=!1);let t=[/*#__PURE__*/(0,v.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push(/*#__PURE__*/(0,v.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}(ej),e$=[],eI={};ev&&(eI.beforeInteractive=[].concat(ev()).filter(e=>"beforeInteractive"===e.props.strategy).map(e=>e.props));let eM=({children:e})=>/*#__PURE__*/(0,v.jsx)(eq.Provider,{value:eP,children:/*#__PURE__*/(0,v.jsx)(eA.Provider,{value:ew.isReady&&ew.query?new URL(ew.asPath,"http://n").searchParams:new URLSearchParams,children:/*#__PURE__*/(0,v.jsx)(eH,{router:ew,isAutoExport:eb,children:/*#__PURE__*/(0,v.jsx)(ek.Provider,{value:function(e){if(!e.isReady||!e.query)return null;let t={};for(let r of Object.keys(function(e){let{parameterizedRoute:t,groups:r}=function(e){let t=ec(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=U.find(t=>e.startsWith(t)),s=e.match(eF);if(t&&s){let{key:e,optional:o,repeat:i}=eB(s[1]);return r[e]={pos:n++,repeat:i,optional:o},"/"+eD(t)+"([^/]+?)"}if(!s)return"/"+eD(e);{let{key:e,repeat:t,optional:o}=eB(s[1]);return r[e]={pos:n++,repeat:t,optional:o},t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}(e.pathname).groups))t[r]=e.query[r];return t}(ew),children:/*#__PURE__*/(0,v.jsx)(H.Provider,{value:ew,children:/*#__PURE__*/(0,v.jsx)(O.Provider,{value:eR,children:/*#__PURE__*/(0,v.jsx)(L.Provider,{value:{updateHead:e=>{eE=e},updateScripts:e=>{e_=e},scripts:eI,mountedInstances:new Set},children:/*#__PURE__*/(0,v.jsx)(A.Provider,{value:e=>e$.push(e),children:/*#__PURE__*/(0,v.jsx)(P.StyleRegistry,{registry:eC,children:/*#__PURE__*/(0,v.jsx)(eN.Provider,{value:W,children:e})})})})})})})})})}),ez=()=>null,eU=({children:e})=>/*#__PURE__*/(0,v.jsxs)(v.Fragment,{children:[/*#__PURE__*/(0,v.jsx)(ez,{}),/*#__PURE__*/(0,v.jsx)(eM,{children:/*#__PURE__*/(0,v.jsxs)(v.Fragment,{children:[b?/*#__PURE__*/(0,v.jsxs)(v.Fragment,{children:[e,/*#__PURE__*/(0,v.jsx)(ez,{})]}):e,/*#__PURE__*/(0,v.jsx)(ez,{})]})})]}),eW={err:x,req:eb?void 0:n,res:eb?void 0:o,pathname:i,query:a,asPath:eS,locale:l.locale,locales:l.locales,defaultLocale:l.defaultLocale,AppTree:e=>/*#__PURE__*/(0,v.jsx)(eU,{children:eK(er,el,{...e,router:ew})}),defaultGetInitialProps:async(e,t={})=>{let{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>/*#__PURE__*/(0,v.jsx)(e,{...t})}),s=eC.styles({nonce:t.nonce});return eC.flush(),{html:r,head:n,styles:s}}},eG=!eh&&(l.nextExport||b&&(eb||ed)),eV=()=>{let e=eC.styles();return eC.flush(),/*#__PURE__*/(0,v.jsx)(v.Fragment,{children:e})};if(h=await X(er,{AppTree:eW.AppTree,Component:el,router:ew,ctx:eW}),(eh||k)&&m&&(h.__N_PREVIEW=!0),eh&&(h.__N_SSG=!0),eh&&!ed){let e,t;try{e=await (0,es.getTracer)().trace(eo.vr.getStaticProps,{spanName:`getStaticProps ${i}`,attributes:{"next.route":i}},()=>N({...ey?{params:z}:void 0,...m?{draftMode:!0,preview:!0,previewData:p}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale,revalidateReason:l.isOnDemandRevalidate?"on-demand":em?"build":"stale"}))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(_.xV);let r=Object.keys(e).filter(e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e);if(r.includes("unstable_revalidate"))throw Error(_.$1);if(r.length)throw Error(e0("getStaticProps",r));if("notFound"in e&&e.notFound){if("/404"===i)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');g.isNotFound=!0}if("redirect"in e&&e.redirect&&"object"==typeof e.redirect){if(e1(e.redirect,n,"getStaticProps"),em)throw Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${n.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:en(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),g.isRedirect=!0}if((b||em)&&!g.isNotFound&&!$(i,"getStaticProps",e.props))throw Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in e){if(e.revalidate&&"export"===l.nextConfigOutput)throw Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof e.revalidate){if(Number.isInteger(e.revalidate)){if(e.revalidate<=0)throw Error(`A page's revalidate option can not be less than or equal to zero for ${n.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`);e.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${n.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),t=e.revalidate}else throw Error(`A page's revalidate option must be seconds expressed as a natural number for ${n.url}. Mixed numbers, such as '${e.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(e.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`)}else if(!0===e.revalidate)t=1;else if(!1===e.revalidate||void 0===e.revalidate)t=!1;else throw Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(e.revalidate)}' for ${n.url}`)}else t=!1;if(h.pageProps=Object.assign({},h.pageProps,"props"in e?e.props:void 0),g.revalidate=t,g.pageData=h,g.isNotFound)return new eT(null,{metadata:g})}if(k&&(h.__N_SSP=!0),k&&!ed){let e;let t=!1;try{e=await (0,es.getTracer)().trace(eo.vr.getServerSideProps,{spanName:`getServerSideProps ${i}`,attributes:{"next.route":i}},async()=>k({req:n,res:o,query:a,resolvedUrl:l.resolvedUrl,...ey?{params:z}:void 0,...!1!==p?{draftMode:!0,preview:!0,previewData:p}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale})),g.revalidate=0}catch(e){throw"object"==typeof e&&null!==e&&"name"in e&&"message"in e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(_.UO);e.props instanceof Promise&&(t=!0);let r=Object.keys(e).filter(e=>"props"!==e&&"redirect"!==e&&"notFound"!==e);if(e.unstable_notFound)throw Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${i}`);if(e.unstable_redirect)throw Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${i}`);if(r.length)throw Error(e0("getServerSideProps",r));if("notFound"in e&&e.notFound){if("/404"===i)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return g.isNotFound=!0,new eT(null,{metadata:g})}if("redirect"in e&&"object"==typeof e.redirect&&(e1(e.redirect,n,"getServerSideProps"),e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:en(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),g.isRedirect=!0),t&&(e.props=await e.props),(b||em)&&!$(i,"getServerSideProps",e.props))throw Error("invariant: getServerSideProps did not return valid props. Please report this.");h.pageProps=Object.assign({},h.pageProps,e.props),g.pageData=h}if(M&&!eh||g.isRedirect)return new eT(JSON.stringify(h),{metadata:g});if(ed&&(h.pageProps={}),J(o)&&!eh)return new eT(null,{metadata:g});let eQ=j;if(eb&&ey){let e;let t=(e=(function(e){let t=/^\/index(\/|$)/.test(e)&&!G(e)?"/index"+e:"/"===e?"/index":q(e);{let{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new Q("Requested and resolved page mismatch: "+t+" "+n)}return t})(i).replace(/\\/g,"/")).startsWith("/index/")&&!G(e)?e.slice(6):"/index"!==e?e:"/";t in eQ.pages&&(eQ={...eQ,pages:{...eQ.pages,[t]:[...eQ.pages[t],...eQ.lowPriorityFiles.filter(e=>e.includes("_buildManifest"))]},lowPriorityFiles:eQ.lowPriorityFiles.filter(e=>!e.includes("_buildManifest"))})}let e4=({children:e})=>ej?e:/*#__PURE__*/(0,v.jsx)("div",{id:"__next",children:e}),e2=async()=>{let e,t;async function r(e){let t=async(t={})=>{if(eW.err&&T)return e&&e(er,el),{html:await eZ(/*#__PURE__*/(0,v.jsx)(e4,{children:/*#__PURE__*/(0,v.jsx)(T,{error:eW.err})})),head:eE};if(b&&(h.router||h.Component))throw Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");let{App:r,Component:n}="function"==typeof t?{App:er,Component:t(el)}:{App:t.enhanceApp?t.enhanceApp(er):er,Component:t.enhanceComponent?t.enhanceComponent(el):el},s=await e(r,n);return await s.allReady,{html:await eu(s),head:eE}},r={...eW,renderPage:t},n=await X(ea,r);if(J(o)&&!eh)return null;if(!n||"string"!=typeof n.html)throw Error(`"${V(ea)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`);return{docProps:n,documentCtx:r}}ea.__NEXT_BUILTIN_DOCUMENT__;let n=(e,t)=>{let r=e||er,n=t||el;return eW.err&&T?/*#__PURE__*/(0,v.jsx)(e4,{children:/*#__PURE__*/(0,v.jsx)(T,{error:eW.err})}):/*#__PURE__*/(0,v.jsx)(e4,{children:/*#__PURE__*/(0,v.jsx)(eU,{children:eK(r,n,{...h,router:ew})})})},s=async(e,t)=>{let r=n(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,es.getTracer)().trace(eo.Wc.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}({ReactDOMServer:w(),element:r})},i=!!ea.getInitialProps,[a,l]=await Promise.all([eZ(eV()),(async()=>{if(i){if(null===(e=await r(s)))return null;let{docProps:t}=e;return t.html}{e={};let t=await s(er,el);return await t.allReady,eu(t)}})()]);if(null===l)return null;let{docProps:d}=e||{};return i?(t=d.styles,eE=d.head):(t=eC.styles(),eC.flush()),{contentHTML:a+l,documentElement:e=>/*#__PURE__*/(0,v.jsx)(ea,{...e,...d}),head:eE,headTags:[],styles:t}};(0,es.getTracer)().setRootSpanAttribute("next.route",l.page);let e3=await (0,es.getTracer)().trace(eo.vr.renderDocument,{spanName:`render route (pages) ${l.page}`,attributes:{"next.route":l.page}},async()=>e2());if(!e3)return new eT(null,{metadata:g});let e8=new Set,e9=new Set;for(let e of e$){let t=E[e];t&&(e8.add(t.id),t.files.forEach(e=>{e9.add(e)}))}let e6=eR.hybrid,{assetPrefix:e5,buildId:e7,customServer:te,defaultLocale:tt,disableOptimizedLoading:tr,domainLocales:tn,locale:ts,locales:to,runtimeConfig:ti}=l,ta={__NEXT_DATA__:{props:h,page:i,query:a,buildId:e7,assetPrefix:""===e5?void 0:e5,runtimeConfig:ti,nextExport:!0===eG||void 0,autoExport:!0===eb||void 0,isFallback:ed,isExperimentalCompile:K,dynamicIds:0===e8.size?void 0:Array.from(e8),err:l.err?(c=l.err,b?(f="server",f=c[eJ]||"server",{name:c.name,source:f,message:eO()(c.message),stack:c.stack,digest:c.digest}):{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}):void 0,gsp:!!N||void 0,gssp:!!k||void 0,customServer:te,gip:!!eg||void 0,appGip:!ef||void 0,locale:ts,locales:to,defaultLocale:tt,domainLocales:tn,isPreview:!0===m||void 0,notFoundSrcPage:ep&&b?ep:void 0},strictNextHead:l.strictNextHead,buildManifest:eQ,docComponentsRendered:{},dangerousAsPath:ew.asPath,canonicalBase:!l.ampPath&&ee(n,"didStripLocale")?`${l.canonicalBase||""}/${l.locale}`:l.canonicalBase,ampPath:S,inAmpMode:ej,isDevelopment:!!b,hybridAmp:e6,dynamicImports:Array.from(e9),dynamicCssManifest:new Set(l.dynamicCssManifest||[]),assetPrefix:e5,unstable_runtimeJS:C.unstable_runtimeJS,unstable_JsPreload:C.unstable_JsPreload,assetQueryString:ei,scriptLoader:e_,locale:ts,disableOptimizedLoading:tr,head:e3.head,headTags:e3.headTags,styles:e3.styles,crossOrigin:l.crossOrigin,optimizeCss:l.optimizeCss,nextConfigOutput:l.nextConfigOutput,nextScriptWorkers:l.nextScriptWorkers,runtime:Y,largePageDataBytes:l.largePageDataBytes,nextFontManifest:l.nextFontManifest,experimentalClientTraceMetadata:l.experimental.clientTraceMetadata},tl=/*#__PURE__*/(0,v.jsx)(O.Provider,{value:eR,children:/*#__PURE__*/(0,v.jsx)(Z.Provider,{value:ta,children:e3.documentElement(ta)})}),td=await (0,es.getTracer)().trace(eo.vr.renderToString,async()=>eZ(tl)),[tu,tc]=td.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>",2),tp="";td.startsWith(eX)||(tp+=eX),tp+=tu,ej&&(tp+="\x3c!-- __NEXT_DATA__ --\x3e");let th=tp+e3.contentHTML+tc;return new eT(await s(i,th,l,{inAmpMode:ej,hybridAmp:e6}),{metadata:g})}let e2=(e,t,r,n,s)=>e4(e,t,r,n,s,s),e3=/*#__PURE__*/b().createContext(null);function e8(e){let t=(0,x.useContext)(e3);t&&t(e)}class e9 extends g{constructor(e){super(e),this.components=e.components}render(e,t,r){return e4(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}let e6={contexts:f},e5=e9})(),module.exports=n})();
//# sourceMappingURL=pages.runtime.prod.js.map
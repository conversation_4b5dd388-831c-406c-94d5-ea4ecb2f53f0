'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';

interface NavigationContextType {
  isNavigating: boolean;
  setIsNavigating: (navigating: boolean) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isNavigating, setIsNavigating] = useState(false);

  return (
    <NavigationContext.Provider value={{ isNavigating, setIsNavigating }}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigationSafe() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    // Return default values if context is not available
    return {
      isNavigating: false,
      setIsNavigating: () => {},
    };
  }
  return context;
}

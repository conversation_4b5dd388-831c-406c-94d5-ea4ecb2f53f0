{"version": 3, "sources": ["../../../../src/client/components/router-reducer/is-navigating-to-new-root-layout.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../server/app-render/types'\n\nexport function isNavigatingToNewRootLayout(\n  currentTree: FlightRouterState,\n  nextTree: FlightRouterState\n): boolean {\n  // Compare segments\n  const currentTreeSegment = currentTree[0]\n  const nextTreeSegment = nextTree[0]\n\n  // If any segment is different before we find the root layout, the root layout has changed.\n  // E.g. /same/(group1)/layout.js -> /same/(group2)/layout.js\n  // First segment is 'same' for both, keep looking. (group1) changed to (group2) before the root layout was found, it must have changed.\n  if (Array.isArray(currentTreeSegment) && Array.isArray(nextTreeSegment)) {\n    // Compare dynamic param name and type but ignore the value, different values would not affect the current root layout\n    // /[name] - /slug1 and /slug2, both values (slug1 & slug2) still has the same layout /[name]/layout.js\n    if (\n      currentTreeSegment[0] !== nextTreeSegment[0] ||\n      currentTreeSegment[2] !== nextTreeSegment[2]\n    ) {\n      return true\n    }\n  } else if (currentTreeSegment !== nextTreeSegment) {\n    return true\n  }\n\n  // Current tree root layout found\n  if (currentTree[4]) {\n    // If the next tree doesn't have the root layout flag, it must have changed.\n    return !nextTree[4]\n  }\n  // Current tree didn't have its root layout here, must have changed.\n  if (nextTree[4]) {\n    return true\n  }\n  // We can't assume it's `parallelRoutes.children` here in case the root layout is `app/@something/layout.js`\n  // But it's not possible to be more than one parallelRoutes before the root layout is found\n  // TODO-APP: change to traverse all parallel routes\n  const currentTreeChild = Object.values(currentTree[1])[0]\n  const nextTreeChild = Object.values(nextTree[1])[0]\n  if (!currentTreeChild || !nextTreeChild) return true\n  return isNavigatingToNewRootLayout(currentTreeChild, nextTreeChild)\n}\n"], "names": ["isNavigatingToNewRootLayout", "currentTree", "nextTree", "currentTreeSegment", "nextTreeSegment", "Array", "isArray", "currentTreeChild", "Object", "values", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,4BACdC,WAA8B,EAC9BC,QAA2B;IAE3B,mBAAmB;IACnB,MAAMC,qBAAqBF,WAAW,CAAC,EAAE;IACzC,MAAMG,kBAAkBF,QAAQ,CAAC,EAAE;IAEnC,2FAA2F;IAC3F,4DAA4D;IAC5D,uIAAuI;IACvI,IAAIG,MAAMC,OAAO,CAACH,uBAAuBE,MAAMC,OAAO,CAACF,kBAAkB;QACvE,sHAAsH;QACtH,uGAAuG;QACvG,IACED,kBAAkB,CAAC,EAAE,KAAKC,eAAe,CAAC,EAAE,IAC5CD,kBAAkB,CAAC,EAAE,KAAKC,eAAe,CAAC,EAAE,EAC5C;YACA,OAAO;QACT;IACF,OAAO,IAAID,uBAAuBC,iBAAiB;QACjD,OAAO;IACT;IAEA,iCAAiC;IACjC,IAAIH,WAAW,CAAC,EAAE,EAAE;QAClB,4EAA4E;QAC5E,OAAO,CAACC,QAAQ,CAAC,EAAE;IACrB;IACA,oEAAoE;IACpE,IAAIA,QAAQ,CAAC,EAAE,EAAE;QACf,OAAO;IACT;IACA,4GAA4G;IAC5G,2FAA2F;IAC3F,mDAAmD;IACnD,MAAMK,mBAAmBC,OAAOC,MAAM,CAACR,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IACzD,MAAMS,gBAAgBF,OAAOC,MAAM,CAACP,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;IACnD,IAAI,CAACK,oBAAoB,CAACG,eAAe,OAAO;IAChD,OAAOV,4BAA4BO,kBAAkBG;AACvD"}
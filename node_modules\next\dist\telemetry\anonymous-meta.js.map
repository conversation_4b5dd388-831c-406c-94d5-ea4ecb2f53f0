{"version": 3, "sources": ["../../src/telemetry/anonymous-meta.ts"], "sourcesContent": ["import isDockerFunction from 'next/dist/compiled/is-docker'\nimport isWslBoolean from 'next/dist/compiled/is-wsl'\nimport os from 'os'\n\nimport * as ciEnvironment from '../server/ci-info'\n\ntype AnonymousMeta = {\n  systemPlatform: NodeJS.Platform\n  systemRelease: string\n  systemArchitecture: string\n  cpuCount: number\n  cpuModel: string | null\n  cpuSpeed: number | null\n  memoryInMb: number\n  isDocker: boolean\n  isNowDev: boolean\n  isWsl: boolean\n  isCI: boolean\n  ciName: string | null\n  nextVersion: string\n}\n\nlet traits: AnonymousMeta | undefined\n\nexport function getAnonymousMeta(): AnonymousMeta {\n  if (traits) {\n    return traits\n  }\n\n  const cpus = os.cpus() || []\n  const { NOW_REGION } = process.env\n  traits = {\n    // Software information\n    systemPlatform: os.platform(),\n    systemRelease: os.release(),\n    systemArchitecture: os.arch(),\n    // Machine information\n    cpuCount: cpus.length,\n    cpuModel: cpus.length ? cpus[0].model : null,\n    cpuSpeed: cpus.length ? cpus[0].speed : null,\n    memoryInMb: Math.trunc(os.totalmem() / Math.pow(1024, 2)),\n    // Environment information\n    isDocker: isDockerFunction(),\n    isNowDev: NOW_REGION === 'dev1',\n    isWsl: isWslBoolean,\n    isCI: ciEnvironment.isCI,\n    ciName: (ciEnvironment.isCI && ciEnvironment.name) || null,\n    nextVersion: process.env.__NEXT_VERSION as string,\n  }\n\n  return traits\n}\n"], "names": ["getAnonymousMeta", "traits", "cpus", "os", "NOW_REGION", "process", "env", "systemPlatform", "platform", "systemRelease", "release", "systemArchitecture", "arch", "cpuCount", "length", "cpuModel", "model", "cpuSpeed", "speed", "memoryInMb", "Math", "trunc", "totalmem", "pow", "is<PERSON>ock<PERSON>", "isDockerFunction", "isNowDev", "isWsl", "isWslBoolean", "isCI", "ciEnvironment", "ciName", "name", "nextVersion", "__NEXT_VERSION"], "mappings": ";;;;+BAwBgBA;;;eAAAA;;;iEAxBa;8DACJ;2DACV;gEAEgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B,IAAIC;AAEG,SAASD;IACd,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,MAAMC,OAAOC,WAAE,CAACD,IAAI,MAAM,EAAE;IAC5B,MAAM,EAAEE,UAAU,EAAE,GAAGC,QAAQC,GAAG;IAClCL,SAAS;QACP,uBAAuB;QACvBM,gBAAgBJ,WAAE,CAACK,QAAQ;QAC3BC,eAAeN,WAAE,CAACO,OAAO;QACzBC,oBAAoBR,WAAE,CAACS,IAAI;QAC3B,sBAAsB;QACtBC,UAAUX,KAAKY,MAAM;QACrBC,UAAUb,KAAKY,MAAM,GAAGZ,IAAI,CAAC,EAAE,CAACc,KAAK,GAAG;QACxCC,UAAUf,KAAKY,MAAM,GAAGZ,IAAI,CAAC,EAAE,CAACgB,KAAK,GAAG;QACxCC,YAAYC,KAAKC,KAAK,CAAClB,WAAE,CAACmB,QAAQ,KAAKF,KAAKG,GAAG,CAAC,MAAM;QACtD,0BAA0B;QAC1BC,UAAUC,IAAAA,iBAAgB;QAC1BC,UAAUtB,eAAe;QACzBuB,OAAOC,cAAY;QACnBC,MAAMC,QAAcD,IAAI;QACxBE,QAAQ,AAACD,QAAcD,IAAI,IAAIC,QAAcE,IAAI,IAAK;QACtDC,aAAa5B,QAAQC,GAAG,CAAC4B,cAAc;IACzC;IAEA,OAAOjC;AACT"}
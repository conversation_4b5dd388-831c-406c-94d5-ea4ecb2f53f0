{"version": 3, "sources": ["../../src/build/next-dir-paths.ts"], "sourcesContent": ["import path from 'path'\n\nexport const NEXT_PROJECT_ROOT = path.join(__dirname, '..', '..')\nexport const NEXT_PROJECT_ROOT_DIST = path.join(NEXT_PROJECT_ROOT, 'dist')\nexport const NEXT_PROJECT_ROOT_DIST_CLIENT = path.join(\n  NEXT_PROJECT_ROOT_DIST,\n  'client'\n)\n"], "names": ["path", "NEXT_PROJECT_ROOT", "join", "__dirname", "NEXT_PROJECT_ROOT_DIST", "NEXT_PROJECT_ROOT_DIST_CLIENT"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,OAAO,MAAMC,oBAAoBD,KAAKE,IAAI,CAACC,WAAW,MAAM,MAAK;AACjE,OAAO,MAAMC,yBAAyBJ,KAAKE,IAAI,CAACD,mBAAmB,QAAO;AAC1E,OAAO,MAAMI,gCAAgCL,KAAKE,IAAI,CACpDE,wBACA,UACD"}
export interface Model {
  id: string;
  name: string;
  display_name?: string;
  provider: string;
  description?: string;
  apiUrl?: string;
}

export interface LLMProvider {
  id: string;
  name: string;
  apiBaseUrl: string;
  models: Model[];
}

export const llmProviders: LLMProvider[] = [
  { id: "openai", name: "OpenAI", apiBaseUrl: "https://api.openai.com/v1/chat/completions", models: [] },
  { id: "anthropic", name: "Anthropic", apiBaseUrl: "https://api.anthropic.com/v1/messages", models: [] },
  { id: "google", name: "Google AI", apiBaseUrl: "https://generativelanguage.googleapis.com/v1beta/models", models: [] },
  { id: "mistral", name: "Mistral AI", apiBaseUrl: "https://api.mistral.ai/v1/chat/completions", models: [] },
  { id: "cohere", name: "Cohere", apiBaseUrl: "https://api.cohere.ai/v1/chat", models: [] },
  { id: "huggingface", name: "Hugging Face", apiBaseUrl: "https://api-inference.huggingface.co/models", models: [] },
  { id: "replicate", name: "Replicate", apiBaseUrl: "https://api.replicate.com/v1/predictions", models: [] },
  { id: "together", name: "Together AI", apiBaseUrl: "https://api.together.xyz/v1/chat/completions", models: [] },
  { id: "groq", name: "Groq", apiBaseUrl: "https://api.groq.com/openai/v1/chat/completions", models: [] },
  { id: "fireworks", name: "Fireworks AI", apiBaseUrl: "https://api.fireworks.ai/inference/v1/chat/completions", models: [] },
  { id: "perplexity", name: "Perplexity", apiBaseUrl: "https://api.perplexity.ai/chat/completions", models: [] },
  { id: "deepseek", name: "DeepSeek", apiBaseUrl: "https://api.deepseek.com/chat/completions", models: [] },
  { id: "xai", name: "xAI (Grok)", apiBaseUrl: "https://api.x.ai/v1/chat/completions", models: [] },
  { id: "openrouter", name: "OpenRouter", apiBaseUrl: "https://openrouter.ai/api/v1/chat/completions", models: [] },
];

export const getAllModels = (): Model[] => {
  // This function might need re-evaluation if its direct use of llmProviders.models is critical elsewhere.
  // For now, it will return an empty array as all nested models arrays are empty.
  return llmProviders.reduce((acc, provider) => acc.concat(provider.models), [] as Model[]);
};

export const getModelById = (providerId: string, modelId: string): Model | undefined => {
  const provider = llmProviders.find(p => p.id === providerId);
  if (!provider) return undefined;
  return provider.models.find(m => m.id === modelId);
};

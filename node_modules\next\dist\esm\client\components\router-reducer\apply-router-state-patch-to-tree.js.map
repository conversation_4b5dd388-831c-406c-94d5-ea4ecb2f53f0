{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\n\n/**\n * Deep merge of the two router states. Parallel route keys are preserved if the patch doesn't have them.\n */\nfunction applyPatch(\n  initialTree: FlightRouterState,\n  patchTree: FlightRouterState\n): FlightRouterState {\n  const [initialSegment, initialParallelRoutes] = initialTree\n  const [patchSegment, patchParallelRoutes] = patchTree\n\n  // if the applied patch segment is __DEFAULT__ then it can be ignored in favor of the initial tree\n  // this is because the __DEFAULT__ segment is used as a placeholder on navigation\n  if (\n    patchSegment === DEFAULT_SEGMENT_KEY &&\n    initialSegment !== DEFAULT_SEGMENT_KEY\n  ) {\n    return initialTree\n  }\n\n  if (matchSegment(initialSegment, patchSegment)) {\n    const newParallelRoutes: FlightRouterState[1] = {}\n    for (const key in initialParallelRoutes) {\n      const isInPatchTreeParallelRoutes =\n        typeof patchParallelRoutes[key] !== 'undefined'\n      if (isInPatchTreeParallelRoutes) {\n        newParallelRoutes[key] = applyPatch(\n          initialParallelRoutes[key],\n          patchParallelRoutes[key]\n        )\n      } else {\n        newParallelRoutes[key] = initialParallelRoutes[key]\n      }\n    }\n\n    for (const key in patchParallelRoutes) {\n      if (newParallelRoutes[key]) {\n        continue\n      }\n\n      newParallelRoutes[key] = patchParallelRoutes[key]\n    }\n\n    const tree: FlightRouterState = [initialSegment, newParallelRoutes]\n\n    // Copy over the existing tree\n    if (initialTree[2]) {\n      tree[2] = initialTree[2]\n    }\n\n    if (initialTree[3]) {\n      tree[3] = initialTree[3]\n    }\n\n    if (initialTree[4]) {\n      tree[4] = initialTree[4]\n    }\n\n    return tree\n  }\n\n  return patchTree\n}\n\n/**\n * Apply the router state from the Flight response, but skip patching default segments.\n * Useful for patching the router cache when navigating, where we persist the existing default segment if there isn't a new one.\n * Creates a new router state tree.\n */\nexport function applyRouterStatePatchToTree(\n  flightSegmentPath: FlightSegmentPath,\n  flightRouterState: FlightRouterState,\n  treePatch: FlightRouterState,\n  path: string\n): FlightRouterState | null {\n  const [segment, parallelRoutes, url, refetch, isRootLayout] =\n    flightRouterState\n\n  // Root refresh\n  if (flightSegmentPath.length === 1) {\n    const tree: FlightRouterState = applyPatch(flightRouterState, treePatch)\n\n    addRefreshMarkerToActiveParallelSegments(tree, path)\n\n    return tree\n  }\n\n  const [currentSegment, parallelRouteKey] = flightSegmentPath\n\n  // Tree path returned from the server should always match up with the current tree in the browser\n  if (!matchSegment(currentSegment, segment)) {\n    return null\n  }\n\n  const lastSegment = flightSegmentPath.length === 2\n\n  let parallelRoutePatch\n  if (lastSegment) {\n    parallelRoutePatch = applyPatch(parallelRoutes[parallelRouteKey], treePatch)\n  } else {\n    parallelRoutePatch = applyRouterStatePatchToTree(\n      getNextFlightSegmentPath(flightSegmentPath),\n      parallelRoutes[parallelRouteKey],\n      treePatch,\n      path\n    )\n\n    if (parallelRoutePatch === null) {\n      return null\n    }\n  }\n\n  const tree: FlightRouterState = [\n    flightSegmentPath[0],\n    {\n      ...parallelRoutes,\n      [parallelRouteKey]: parallelRoutePatch,\n    },\n    url,\n    refetch,\n  ]\n\n  // Current segment is the root layout\n  if (isRootLayout) {\n    tree[4] = true\n  }\n\n  addRefreshMarkerToActiveParallelSegments(tree, path)\n\n  return tree\n}\n"], "names": ["DEFAULT_SEGMENT_KEY", "getNextFlightSegmentPath", "matchSegment", "addRefreshMarkerToActiveParallelSegments", "applyPatch", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "applyRouterStatePatchToTree", "flightSegmentPath", "flightRouterState", "treePatch", "path", "segment", "parallelRoutes", "url", "refetch", "isRootLayout", "length", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch"], "mappings": "AAIA,SAASA,mBAAmB,QAAQ,8BAA6B;AACjE,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,wCAAwC,QAAQ,uCAAsC;AAE/F;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B;IAE5B,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGH;IAChD,MAAM,CAACI,cAAcC,oBAAoB,GAAGJ;IAE5C,kGAAkG;IAClG,iFAAiF;IACjF,IACEG,iBAAiBT,uBACjBO,mBAAmBP,qBACnB;QACA,OAAOK;IACT;IAEA,IAAIH,aAAaK,gBAAgBE,eAAe;QAC9C,MAAME,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAOJ,sBAAuB;YACvC,MAAMK,8BACJ,OAAOH,mBAAmB,CAACE,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGR,WACvBI,qBAAqB,CAACI,IAAI,EAC1BF,mBAAmB,CAACE,IAAI;YAE5B,OAAO;gBACLD,iBAAiB,CAACC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOF,oBAAqB;YACrC,IAAIC,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACP;YAAgBI;SAAkB;QAEnE,8BAA8B;QAC9B,IAAIN,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOS;IACT;IAEA,OAAOR;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASS,4BACdC,iBAAoC,EACpCC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAY;IAEZ,MAAM,CAACC,SAASC,gBAAgBC,KAAKC,SAASC,aAAa,GACzDP;IAEF,eAAe;IACf,IAAID,kBAAkBS,MAAM,KAAK,GAAG;QAClC,MAAMX,OAA0BV,WAAWa,mBAAmBC;QAE9Df,yCAAyCW,MAAMK;QAE/C,OAAOL;IACT;IAEA,MAAM,CAACY,gBAAgBC,iBAAiB,GAAGX;IAE3C,iGAAiG;IACjG,IAAI,CAACd,aAAawB,gBAAgBN,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMQ,cAAcZ,kBAAkBS,MAAM,KAAK;IAEjD,IAAII;IACJ,IAAID,aAAa;QACfC,qBAAqBzB,WAAWiB,cAAc,CAACM,iBAAiB,EAAET;IACpE,OAAO;QACLW,qBAAqBd,4BACnBd,yBAAyBe,oBACzBK,cAAc,CAACM,iBAAiB,EAChCT,WACAC;QAGF,IAAIU,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMf,OAA0B;QAC9BE,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGK,cAAc;YACjB,CAACM,iBAAiB,EAAEE;QACtB;QACAP;QACAC;KACD;IAED,qCAAqC;IACrC,IAAIC,cAAc;QAChBV,IAAI,CAAC,EAAE,GAAG;IACZ;IAEAX,yCAAyCW,MAAMK;IAE/C,OAAOL;AACT"}